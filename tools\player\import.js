/**
 * Player Import Tool for CARX HLTV
 * 
 * This script imports/updates player data from a CSV file into Sanity CMS.
 * Only updates ELO and nickname fields, preserves all other existing data.
 * 
 * Usage: npm run import-players
 */

const { createAndTestClient } = require('../utils/sanity-client')
const { parsePlayerCSV } = require('../utils/csv-parser')
const { logToolStart, logToolEnd, logProgress, logSuccess, logError, logStats, saveReport } = require('../utils/logger')

const BATCH_SIZE = 10

async function importPlayers() {
  try {
    logToolStart('PLAYER IMPORT TOOL', 'Import/Update Player Data from CSV')
    
    // Initialize Sanity client and test connection
    const client = await createAndTestClient()
    
    // Parse CSV data
    const csvPlayers = await parsePlayerCSV()
    
    if (csvPlayers.length === 0) {
      logError('No valid players found in CSV file')
      process.exit(1)
    }
    
    // Fetch existing players from Sanity
    console.log('🔍 Fetching existing players from Sanity...')
    const existingPlayers = await client.fetch(`
      *[_type == "player" && !(_id in path("drafts.**"))] {
        _id,
        nickname,
        elo,
        name,
        image
      }
    `)
    
    // Create lookup map for existing players
    const existingPlayersMap = new Map()
    existingPlayers.forEach(player => {
      existingPlayersMap.set(player.nickname.toLowerCase().trim(), player)
    })
    
    logSuccess(`Found ${existingPlayers.length} existing players`)
    
    // Separate players into updates and creates
    const playersToUpdate = []
    const playersToCreate = []
    
    csvPlayers.forEach(player => {
      const existing = existingPlayersMap.get(player.nickname.toLowerCase().trim())
      if (existing) {
        // Only update if ELO has changed
        if (existing.elo !== player.elo) {
          playersToUpdate.push({
            id: existing._id,
            nickname: player.nickname,
            elo: player.elo,
            name: player.name
          })
        }
      } else {
        playersToCreate.push(player)
      }
    })
    
    logStats({
      csvPlayers: csvPlayers.length,
      existingPlayers: existingPlayers.length,
      playersToUpdate: playersToUpdate.length,
      playersToCreate: playersToCreate.length
    })
    
    // Process updates in batches
    let updateCount = 0
    if (playersToUpdate.length > 0) {
      console.log('\n🔄 Updating existing players...')
      
      for (let i = 0; i < playersToUpdate.length; i += BATCH_SIZE) {
        const batch = playersToUpdate.slice(i, i + BATCH_SIZE)
        
        for (const player of batch) {
          try {
            await client
              .patch(player.id)
              .set({
                elo: player.elo,
                nickname: player.nickname
                // Note: Deliberately not updating 'name' field to preserve existing real names
              })
              .commit()
            
            updateCount++
            logProgress(updateCount, playersToUpdate.length, 'Updating players')
          } catch (error) {
            logError(`Failed to update ${player.nickname}: ${error.message}`)
          }
        }
      }
    }
    
    // Process creates in batches
    let createCount = 0
    if (playersToCreate.length > 0) {
      console.log('\n➕ Creating new players...')
      
      for (let i = 0; i < playersToCreate.length; i += BATCH_SIZE) {
        const batch = playersToCreate.slice(i, i + BATCH_SIZE)
        
        for (const player of batch) {
          try {
            await client.create({
              _type: 'player',
              nickname: player.nickname,
              name: player.name || '',
              elo: player.elo
            })
            
            createCount++
            logProgress(createCount, playersToCreate.length, 'Creating players')
          } catch (error) {
            logError(`Failed to create ${player.nickname}: ${error.message}`)
          }
        }
      }
    }
    
    // Generate report
    const report = {
      summary: {
        csvPlayersProcessed: csvPlayers.length,
        existingPlayersFound: existingPlayers.length,
        playersUpdated: updateCount,
        playersCreated: createCount,
        totalChanges: updateCount + createCount
      },
      updatedPlayers: playersToUpdate.slice(0, updateCount),
      createdPlayers: playersToCreate.slice(0, createCount)
    }
    
    const reportPath = saveReport(report, 'player-import-report.json')
    
    logStats({
      playersUpdated: updateCount,
      playersCreated: createCount,
      totalChanges: updateCount + createCount
    }, 'IMPORT RESULTS')
    
    logToolEnd('PLAYER IMPORT TOOL', true)
    
  } catch (error) {
    logError(`Import failed: ${error.message}`)
    logToolEnd('PLAYER IMPORT TOOL', false)
    process.exit(1)
  }
}

// Run the import
importPlayers()
