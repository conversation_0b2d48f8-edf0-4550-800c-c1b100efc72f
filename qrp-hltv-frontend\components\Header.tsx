import Link from 'next/link'
import Image from 'next/image'
import { useState } from 'react'
import { useLanguage } from '../lib/LanguageContext'
import LanguageSelector from './LanguageSelector'

interface HeaderProps {
  currentPage?: 'news' | 'tournaments' | 'players' | 'teams' | 'partners' | 'contact'
}

export default function Header({ currentPage }: HeaderProps) {
  const { t } = useLanguage();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <header className="sticky top-0 z-50 bg-zinc-950/90 backdrop-blur border-b border-zinc-800 shadow-lg">
      <div className="max-w-6xl mx-auto flex items-center justify-between px-4 sm:px-6 py-3 sm:py-4 min-h-[72px]">
        <Link href="/" className="flex items-center flex-shrink-0 space-x-4">
          <Image
            src="/QRP_LOGO.webp"
            alt="QRP HLTV"
            width={160}
            height={53}
            className="h-16 sm:h-20 w-auto"
            priority
          />
          <span className="text-3xl sm:text-4xl font-extrabold text-transparent bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text tracking-tight">
            HLTV
          </span>
        </Link>
        <div className="flex items-center space-x-4 sm:space-x-6 lg:space-x-8">
          <nav className="hidden sm:flex space-x-4 lg:space-x-6 xl:space-x-8">
            <Link
              href="/news"
              className={`transition font-semibold text-sm lg:text-base whitespace-nowrap ${currentPage === 'news' ? 'text-blue-400 hover:text-blue-300' : 'hover:text-blue-400'}`}
            >
              {t('news')}
            </Link>
            <Link
              href="/tournaments"
              className={`transition font-semibold text-sm lg:text-base whitespace-nowrap ${currentPage === 'tournaments' ? 'text-green-400 hover:text-green-300' : 'hover:text-green-400'}`}
            >
              {t('tournaments')}
            </Link>
            <Link
              href="/players"
              className={`transition font-semibold text-sm lg:text-base whitespace-nowrap ${currentPage === 'players' ? 'text-purple-400 hover:text-purple-300' : 'hover:text-purple-400'}`}
            >
              {t('players')}
            </Link>
            <Link
              href="/teams"
              className={`transition font-semibold text-sm lg:text-base whitespace-nowrap ${currentPage === 'teams' ? 'text-orange-400 hover:text-orange-300' : 'hover:text-orange-400'}`}
            >
              {t('teams')}
            </Link>
            <Link
              href="/partners"
              className={`transition font-semibold text-sm lg:text-base whitespace-nowrap ${currentPage === 'partners' ? 'text-yellow-400 hover:text-yellow-300' : 'hover:text-yellow-400'}`}
            >
              {t('partners')}
            </Link>
            <Link
              href="/contact"
              className={`transition font-semibold text-sm lg:text-base whitespace-nowrap ${currentPage === 'contact' ? 'text-cyan-400 hover:text-cyan-300' : 'hover:text-cyan-400'}`}
            >
              {t('contact')}
            </Link>
          </nav>
          <div className="flex items-center space-x-3 sm:space-x-4">
            <LanguageSelector />
            <a
              href="https://discord.gg/tGW2R5eDE6"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center space-x-2 px-3 sm:px-4 py-2 bg-[#5865F2] hover:bg-[#4752C4] transition rounded-lg font-semibold text-white text-sm sm:text-base"
            >
              <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515a.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0a12.64 12.64 0 0 0-.617-1.25a.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057a19.9 19.9 0 0 0 5.993 3.03a.078.078 0 0 0 .084-.028a14.09 14.09 0 0 0 1.226-1.994a.076.076 0 0 0-.041-.106a13.107 13.107 0 0 1-1.872-.892a.077.077 0 0 1-.008-.128a10.2 10.2 0 0 0 .372-.292a.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127a12.299 12.299 0 0 1-1.873.892a.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028a19.839 19.839 0 0 0 6.002-3.03a.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419c0-1.333.956-2.419 2.157-2.419c1.21 0 2.176 1.096 2.157 2.42c0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419c0-1.333.955-2.419 2.157-2.419c1.21 0 2.176 1.096 2.157 2.42c0 1.333-.946 2.418-2.157 2.418z"/>
              </svg>
              <span className="hidden sm:inline">Discord</span>
            </a>

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="sm:hidden flex items-center justify-center w-10 h-10 rounded-lg hover:bg-zinc-800 transition"
              aria-label="Toggle mobile menu"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isMobileMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMobileMenuOpen && (
        <div className="sm:hidden bg-zinc-950/95 backdrop-blur border-t border-zinc-800">
          <nav className="max-w-6xl mx-auto px-4 py-4 space-y-3">
            <Link
              href="/news"
              className={`block py-2 px-3 rounded-lg transition font-semibold ${currentPage === 'news' ? 'text-blue-400 bg-blue-400/10' : 'hover:text-blue-400 hover:bg-zinc-800'}`}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              {t('news')}
            </Link>
            <Link
              href="/tournaments"
              className={`block py-2 px-3 rounded-lg transition font-semibold ${currentPage === 'tournaments' ? 'text-green-400 bg-green-400/10' : 'hover:text-green-400 hover:bg-zinc-800'}`}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              {t('tournaments')}
            </Link>
            <Link
              href="/players"
              className={`block py-2 px-3 rounded-lg transition font-semibold ${currentPage === 'players' ? 'text-purple-400 bg-purple-400/10' : 'hover:text-purple-400 hover:bg-zinc-800'}`}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              {t('players')}
            </Link>
            <Link
              href="/teams"
              className={`block py-2 px-3 rounded-lg transition font-semibold ${currentPage === 'teams' ? 'text-orange-400 bg-orange-400/10' : 'hover:text-orange-400 hover:bg-zinc-800'}`}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              {t('teams')}
            </Link>
            <Link
              href="/partners"
              className={`block py-2 px-3 rounded-lg transition font-semibold ${currentPage === 'partners' ? 'text-yellow-400 bg-yellow-400/10' : 'hover:text-yellow-400 hover:bg-zinc-800'}`}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              {t('partners')}
            </Link>
            <Link
              href="/contact"
              className={`block py-2 px-3 rounded-lg transition font-semibold ${currentPage === 'contact' ? 'text-cyan-400 bg-cyan-400/10' : 'hover:text-cyan-400 hover:bg-zinc-800'}`}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              {t('contact')}
            </Link>
          </nav>
        </div>
      )}
    </header>
  )
}