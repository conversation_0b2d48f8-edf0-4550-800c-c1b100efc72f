import {defineField, defineType} from 'sanity'

export default defineType({
  name: 'supportRequest',
  title: 'Support Request',
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: 'Name',
      type: 'string',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'discord',
      title: 'Discord Tag',
      type: 'string',
      validation: (Rule) => Rule.required().regex(/^@[a-z0-9._]{2,32}$/, {
        name: 'Discord tag format',
        invert: false
      }).error('Please enter a valid Discord tag (e.g., @username)'),
    }),
    defineField({
      name: 'subject',
      title: 'Subject',
      type: 'string',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'message',
      title: 'Message',
      type: 'array',
      of: [{ type: 'block' }],
      validation: (Rule) => Rule.required().min(1),
    }),
    defineField({
      name: 'status',
      title: 'Status',
      type: 'string',
      options: {
        list: [
          {title: 'New', value: 'new'},
          {title: 'In Progress', value: 'in_progress'},
          {title: 'Resolved', value: 'resolved'},
          {title: 'Closed', value: 'closed'},
        ],
      },
      initialValue: 'new',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'priority',
      title: 'Priority',
      type: 'string',
      options: {
        list: [
          {title: 'Low', value: 'low'},
          {title: 'Medium', value: 'medium'},
          {title: 'High', value: 'high'},
          {title: 'Urgent', value: 'urgent'},
        ],
      },
      initialValue: 'medium',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'submittedAt',
      title: 'Submitted At',
      type: 'datetime',
      initialValue: () => new Date().toISOString(),
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'adminNotes',
      title: 'Admin Notes',
      type: 'array',
      of: [{ type: 'block' }],
      description: 'Internal notes for admin use only',
    }),
    defineField({
      name: 'assignedTo',
      title: 'Assigned To',
      type: 'string',
      description: 'Admin or team member assigned to handle this request',
    }),
  ],
  preview: {
    select: {
      title: 'subject',
      subtitle: 'name',
      description: 'status',
    },
    prepare(selection) {
      const {title, subtitle, description} = selection
      return {
        title: title,
        subtitle: `From: ${subtitle}`,
        description: `Status: ${description}`,
      }
    },
  },
  orderings: [
    {
      title: 'Submitted Date, New',
      name: 'submittedAtDesc',
      by: [
        {field: 'submittedAt', direction: 'desc'}
      ]
    },
    {
      title: 'Status',
      name: 'statusAsc',
      by: [
        {field: 'status', direction: 'asc'},
        {field: 'submittedAt', direction: 'desc'}
      ]
    },
    {
      title: 'Priority',
      name: 'priorityDesc',
      by: [
        {field: 'priority', direction: 'desc'},
        {field: 'submittedAt', direction: 'desc'}
      ]
    },
  ],
})
