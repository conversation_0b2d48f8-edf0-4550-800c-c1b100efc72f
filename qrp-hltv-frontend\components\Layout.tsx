import Header from './Header'
import Footer from './Footer'

interface LayoutProps {
  children: React.ReactNode
  currentPage?: 'news' | 'tournaments' | 'players' | 'teams' | 'partners' | 'contact'
}

export default function Layout({ children, currentPage }: LayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-zinc-950 via-zinc-900 to-zinc-800 text-zinc-100 flex flex-col">
      <Header currentPage={currentPage} />
      <div className="flex-1">
        {children}
      </div>
      <div className="max-w-6xl mx-auto w-full px-4">
        <Footer />
      </div>
    </div>
  )
}
