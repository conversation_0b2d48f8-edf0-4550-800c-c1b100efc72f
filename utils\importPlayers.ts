const { createClient } = require('@sanity/client')
const fs = require('fs')
const { parse } = require('csv-parse')
const dotenv = require('dotenv')

// Load environment variables
dotenv.config()

// Initialize Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,
  token: process.env.SANITY_TOKEN!,
  apiVersion: '2024-03-24',
  useCdn: false,
})

interface Player {
  nickname: string
  name: string
  elo: number
}

interface CsvRecord {
  Nickname: string
  Name: string
  Elo: string
}

async function importPlayers() {
  const players: Player[] = []
  
  // Read the CSV file
  const fileContent = fs.readFileSync('players list.csv', 'utf-8')
  
  // Parse CSV
  await new Promise((resolve, reject) => {
    parse(fileContent, {
      columns: true,
      skip_empty_lines: true,
      trim: true,
    }, (err: any, records: CsvRecord[]) => {
      if (err) {
        console.error('Error parsing CSV:', err)
        reject(err)
        return
      }

      // Process each record
      records.forEach((record: CsvRecord) => {
        if (record.Nickname && record.Nickname.trim()) {
          players.push({
            nickname: record.Nickname.trim(),
            name: record.Name ? record.Name.trim() : '',
            elo: parseInt(record.Elo) || 1000,
          })
        }
      })
      resolve(records)
    })
  })

  // Import to Sanity
  console.log(`Importing ${players.length} players to Sanity...`)
  
  for (const player of players) {
    try {
      // Check if player already exists
      const existingPlayer = await client.fetch(
        `*[_type == "player" && nickname == $nickname][0]`,
        { nickname: player.nickname }
      )

      if (existingPlayer) {
        // Update existing player
        await client
          .patch(existingPlayer._id)
          .set({
            name: player.name,
            elo: player.elo,
          })
          .commit()
        console.log(`Updated player: ${player.nickname}`)
      } else {
        // Create new player
        await client.create({
          _type: 'player',
          ...player,
        })
        console.log(`Created player: ${player.nickname}`)
      }
    } catch (error) {
      console.error(`Error processing player ${player.nickname}:`, error)
    }
  }

  console.log('Import completed!')
}

// Run the import
importPlayers().catch(console.error) 