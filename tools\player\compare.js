/**
 * Player Comparison Tool for CARX HLTV
 * 
 * Compares players between Sanity CMS and CSV file to identify discrepancies.
 * 
 * Usage: npm run compare-players
 */

const { createAndTestClient } = require('../utils/sanity-client')
const { parsePlayerCSV } = require('../utils/csv-parser')
const { logToolStart, logToolEnd, logSubsection, logStats, saveReport } = require('../utils/logger')

async function fetchSanityPlayers(client) {
  console.log('🔍 Fetching players from Sanity CMS...')
  const players = await client.fetch(`
    *[_type == "player" && !(_id in path("drafts.**"))] {
      _id,
      nickname,
      name,
      elo
    }
  `)
  console.log(`✅ Found ${players.length} players in Sanity CMS`)
  return players
}

function comparePlayerLists(sanityPlayers, csvPlayers) {
  // Create sets of nicknames for easy comparison
  const sanityNicknames = new Set(sanityPlayers.map(p => p.nickname.toLowerCase().trim()))
  const csvNicknames = new Set(csvPlayers.map(p => p.nickname.toLowerCase().trim()))

  // Find players in Sanity but not in CSV
  const inSanityNotInCSV = sanityPlayers.filter(player => 
    !csvNicknames.has(player.nickname.toLowerCase().trim())
  )

  // Find players in CSV but not in Sanity
  const inCSVNotInSanity = csvPlayers.filter(player => 
    !sanityNicknames.has(player.nickname.toLowerCase().trim())
  )

  return {
    inSanityNotInCSV,
    inCSVNotInSanity,
    totalSanity: sanityPlayers.length,
    totalCSV: csvPlayers.length,
    commonPlayers: sanityPlayers.length - inSanityNotInCSV.length
  }
}

function displayResults(comparison) {
  logStats({
    totalInSanity: comparison.totalSanity,
    totalInCSV: comparison.totalCSV,
    commonPlayers: comparison.commonPlayers,
    onlyInSanity: comparison.inSanityNotInCSV.length,
    onlyInCSV: comparison.inCSVNotInSanity.length
  }, 'COMPARISON SUMMARY')

  if (comparison.inSanityNotInCSV.length > 0) {
    logSubsection('🔍 PLAYERS ONLY IN SANITY (not in CSV)')
    comparison.inSanityNotInCSV.forEach(player => {
      console.log(`  • ${player.nickname} (ELO: ${player.elo || 'N/A'})`)
    })
  }

  if (comparison.inCSVNotInSanity.length > 0) {
    logSubsection('📄 PLAYERS ONLY IN CSV (not in Sanity)')
    comparison.inCSVNotInSanity.forEach(player => {
      console.log(`  • ${player.nickname} (ELO: ${player.elo})`)
    })
  }

  if (comparison.inSanityNotInCSV.length === 0 && comparison.inCSVNotInSanity.length === 0) {
    console.log('\n✅ Perfect match! All players are synchronized between Sanity and CSV.')
  }
}

async function comparePlayersWithCSV() {
  try {
    logToolStart('PLAYER COMPARISON TOOL', 'Compare Sanity CMS vs CSV Data')
    
    // Initialize Sanity client and test connection
    const client = await createAndTestClient()
    
    // Fetch data from both sources
    const [sanityPlayers, csvPlayers] = await Promise.all([
      fetchSanityPlayers(client),
      parsePlayerCSV()
    ])

    if (csvPlayers.length === 0) {
      console.log('⚠️  No active players found in CSV (players with ELO > 0)')
      logToolEnd('PLAYER COMPARISON TOOL', false)
      return
    }

    // Perform comparison
    const comparison = comparePlayerLists(sanityPlayers, csvPlayers)

    // Display results
    displayResults(comparison)

    // Save detailed report
    const report = {
      summary: {
        totalSanity: comparison.totalSanity,
        totalCSV: comparison.totalCSV,
        commonPlayers: comparison.commonPlayers,
        onlyInSanity: comparison.inSanityNotInCSV.length,
        onlyInCSV: comparison.inCSVNotInSanity.length
      },
      playersOnlyInSanity: comparison.inSanityNotInCSV,
      playersOnlyInCSV: comparison.inCSVNotInSanity
    }

    saveReport(report, 'player-comparison-report.json')
    
    logToolEnd('PLAYER COMPARISON TOOL', true)

  } catch (error) {
    console.error('❌ Comparison failed:', error.message)
    logToolEnd('PLAYER COMPARISON TOOL', false)
    process.exit(1)
  }
}

// Run the comparison
comparePlayersWithCSV()
