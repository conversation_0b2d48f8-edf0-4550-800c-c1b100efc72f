[build]
  command = "npm run build-studio && cd qrp-hltv-frontend && npm install && npm run build"
  publish = "qrp-hltv-frontend/.next"

[dev]
  command = "npm run dev --prefix qrp-hltv-frontend"
  targetPort = 3000
  port = 8888
  publish = "qrp-hltv-frontend/public"

[[plugins]]
  package = "@netlify/plugin-nextjs"

# Functions configuration can be set in Netlify UI
# [functions]
#   timeout = "30s"

[[redirects]]
  from = "/studio"
  to = "/studio/index.html"
  status = 200

[[redirects]]
  from = "/studio/*"
  to = "/studio/index.html"
  status = 200

# Let Next.js plugin handle routing - don't add catch-all redirect