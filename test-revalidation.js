// Test script to verify revalidation works for different content types
// Run with: node test-revalidation.js
// Make sure your app is deployed and you have the webhook secret

const testRevalidation = async (documentType = 'partner') => {
  // You'll need to replace these with your actual values
  const WEBHOOK_SECRET = process.env.SANITY_WEBHOOK_SECRET || 'your-webhook-secret-here';
  const SITE_URL = process.env.SITE_URL || 'https://qrp-hltv.com';
  
  console.log(`🧪 Testing ${documentType} revalidation...`);
  console.log(`🌐 Site URL: ${SITE_URL}`);
  console.log(`🔑 Using webhook secret: ${WEBHOOK_SECRET ? WEBHOOK_SECRET.substring(0, 5) + '...' : 'NOT SET'}`);

  // Test data simulating a Sanity webhook
  const testData = {
    _type: documentType,
    _id: `test-${documentType}-${Date.now()}`,
    action: 'update'
  };

  try {
    console.log('📝 Test data:', testData);
    console.log(`🌐 Sending request to ${SITE_URL}/api/test-revalidate?secret=${WEBHOOK_SECRET}&type=${documentType}`);

    const response = await fetch(`${SITE_URL}/api/test-revalidate?secret=${WEBHOOK_SECRET}&type=${documentType}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });

    const result = await response.json();

    console.log('📊 Response status:', response.status);
    console.log('📋 Response data:', JSON.stringify(result, null, 2));

    if (response.ok) {
      console.log(`✅ ${documentType} revalidation test passed!`);
      
      // Check if any paths failed
      const failedPaths = result.results?.filter(r => r.status !== 'success') || [];
      if (failedPaths.length > 0) {
        console.log('⚠️ Some paths failed to revalidate:');
        failedPaths.forEach(path => {
          console.log(`   - ${path.path}: ${path.reason || 'Unknown error'}`);
        });
      }
    } else {
      console.log(`❌ ${documentType} revalidation test failed!`);
      console.log('💥 Error:', result.message);
    }

  } catch (error) {
    console.error(`❌ Test failed with network error:`, error.message);
    console.log('\n🔍 Possible causes:');
    console.log('   - Site is not deployed or URL is incorrect');
    console.log('   - Webhook secret is incorrect');
    console.log('   - Network connectivity issues');
  }
};

// Test both player and partner revalidation
const runAllTests = async () => {
  console.log('🚀 Starting revalidation tests...\n');
  
  await testRevalidation('player');
  console.log('\n' + '='.repeat(50) + '\n');
  await testRevalidation('partner');
  
  console.log('\n✨ All tests completed!');
};

// Only run if this file is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = { testRevalidation, runAllTests };
