import { NextRequest, NextResponse } from 'next/server'

export function middleware(request: NextRequest) {
  // Check if global redirect is enabled via environment variable
  const isRedirectEnabled = process.env.GLOBAL_REDIRECT_ENABLED === 'true'
  
  // If redirect is not enabled, allow normal routing
  if (!isRedirectEnabled) {
    return NextResponse.next()
  }
  
  // Get the current pathname
  const pathname = request.nextUrl.pathname
  
  // Don't redirect if already on the not_allowed page to prevent infinite loops
  if (pathname === '/not_allowed') {
    return NextResponse.next()
  }
  
  // Don't redirect API routes, static files, or Next.js internal routes
  if (
    pathname.startsWith('/api/') ||
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/studio/') ||
    pathname.includes('.') // Static files (images, css, js, etc.)
  ) {
    return NextResponse.next()
  }
  
  // Redirect all other routes to /not_allowed
  const redirectUrl = new URL('/not_allowed', request.url)
  return NextResponse.redirect(redirectUrl)
}

// Configure which paths the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - studio (Sanity Studio)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|studio).*)',
  ],
}
