import type { NextApiRequest, NextApiResponse } from 'next'

// Fallback webhook that triggers a full Netlify rebuild
// Use this if ISR revalidation doesn't work
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  console.log('REBUILD WEBHOOK TRIGGERED');
  console.log('Method:', req.method);
  console.log('Body:', JSON.stringify(req.body, null, 2));
  
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Check for secret
  if (req.query.secret !== process.env.SANITY_WEBHOOK_SECRET) {
    console.error('Invalid webhook secret');
    return res.status(401).json({ message: 'Invalid token' });
  }

  try {
    const buildHookUrl = process.env.NETLIFY_BUILD_HOOK_URL;
    
    if (!buildHookUrl) {
      console.error('NETLIFY_BUILD_HOOK_URL not configured');
      return res.status(500).json({ 
        message: 'Build hook not configured',
        instructions: [
          '1. Go to Netlify dashboard → Site settings → Build & deploy → Build hooks',
          '2. Create a new build hook',
          '3. Add NETLIFY_BUILD_HOOK_URL environment variable with the hook URL'
        ]
      });
    }

    // Get document type for logging
    const documentType = req.body?._type || 'unknown';
    const documentId = req.body?._id || 'unknown';
    
    console.log(`Triggering rebuild for ${documentType} document: ${documentId}`);
    
    // Trigger Netlify build
    const response = await fetch(buildHookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        trigger: 'sanity-webhook',
        documentType,
        documentId,
        timestamp: new Date().toISOString()
      })
    });

    if (response.ok) {
      console.log('✅ Build triggered successfully');
      return res.json({
        success: true,
        message: 'Build triggered successfully',
        documentType,
        documentId,
        timestamp: new Date().toISOString()
      });
    } else {
      console.error('❌ Failed to trigger build:', response.status, response.statusText);
      return res.status(500).json({
        success: false,
        message: 'Failed to trigger build',
        status: response.status,
        statusText: response.statusText
      });
    }

  } catch (error) {
    console.error('Rebuild webhook error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
