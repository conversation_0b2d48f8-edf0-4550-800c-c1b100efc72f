import { defineConfig } from 'sanity'
import { deskTool } from 'sanity/desk'
import { visionTool } from '@sanity/vision'
import { schemaTypes } from './schemaTypes'

const SANITY_PROJECT_ID = process.env.SANITY_PROJECT_ID || 'al3wd5y8'
const SANITY_DATASET = process.env.SANITY_DATASET || 'production'

if (!SANITY_PROJECT_ID) {
  throw new Error('SANITY_PROJECT_ID is required')
}

if (!SANITY_DATASET) {
  throw new Error('SANITY_DATASET is required')
}

export default defineConfig({
  name: 'default',
  title: 'carx-hltv',
  projectId: SANITY_PROJECT_ID,
  dataset: SANITY_DATASET,
  basePath: '/studio',
  plugins: [deskTool(), visionTool()],
  schema: {
    types: schemaTypes,
  },
})
