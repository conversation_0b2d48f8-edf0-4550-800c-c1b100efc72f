const { createClient } = require('@sanity/client')
const fs = require('fs')
const { parse } = require('csv-parse')
const dotenv = require('dotenv')

// Load environment variables
dotenv.config()

// Debug: Log environment variables (without exposing the full token)
console.log('Environment variables loaded:')
console.log('Project ID:', process.env.NEXT_PUBLIC_SANITY_PROJECT_ID)
console.log('Dataset:', process.env.NEXT_PUBLIC_SANITY_DATASET)
console.log('Token exists:', !!process.env.SANITY_TOKEN)
console.log('Token starts with:', process.env.SANITY_TOKEN ? process.env.SANITY_TOKEN.substring(0, 5) + '...' : 'N/A')

// Validate required environment variables
if (!process.env.NEXT_PUBLIC_SANITY_PROJECT_ID) {
  throw new Error('Missing NEXT_PUBLIC_SANITY_PROJECT_ID in environment variables')
}
if (!process.env.NEXT_PUBLIC_SANITY_DATASET) {
  throw new Error('Missing NEXT_PUBLIC_SANITY_DATASET in environment variables')
}
if (!process.env.SANITY_TOKEN) {
  throw new Error('Missing SANITY_TOKEN in environment variables')
}

// Initialize Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  token: process.env.SANITY_TOKEN,
  apiVersion: '2024-03-24',
  useCdn: false,
})

// Test connection and permissions
async function testConnection() {
  try {
    console.log('Testing Sanity connection...')
    const result = await client.fetch('*[_type == "player"][0]')
    console.log('Connection test result:', result ? 'Success' : 'No documents found')
    return true
  } catch (error) {
    console.error('Connection test failed:', error.message)
    return false
  }
}

async function importPlayers() {
  // Test connection first
  const connectionValid = await testConnection()
  if (!connectionValid) {
    console.error('Failed to establish connection to Sanity. Please check your credentials.')
    process.exit(1)
  }

  const players = []
  
  console.log('Reading CSV file...')
  // Read the CSV file
  const fileContent = fs.readFileSync('players list.csv', 'utf-8')
  
  // Parse CSV
  await new Promise((resolve, reject) => {
    parse(fileContent, {
      columns: true,
      skip_empty_lines: true,
      trim: true,
    }, (err, records) => {
      if (err) {
        console.error('Error parsing CSV:', err)
        reject(err)
        return
      }

      // Process each record
      records.forEach((record) => {
        // Skip if this is the header row (where Nickname equals "Nickname")
        if (record.Nickname && record.Nickname.trim() && record.Nickname.toLowerCase() !== 'nickname') {
          const player = {
            nickname: record.Nickname.trim(),
            name: record.Name ? record.Name.trim() : '',
            elo: parseInt(record.Elo) || 1000,
          }
          players.push(player)
          console.log('Found player:', player)
        }
      })
      resolve(records)
    })
  })

  // Import to Sanity
  console.log(`\nImporting ${players.length} players to Sanity...`)
  
  for (const player of players) {
    try {
      // Debug: Log the mutation we're about to make
      console.log(`\nProcessing player: ${player.nickname}`)
      console.log('Document to create/update:', JSON.stringify(player, null, 2))
      
      // Check if player already exists
      const existingPlayer = await client.fetch(
        `*[_type == "player" && nickname == $nickname][0]`,
        { nickname: player.nickname }
      )

      if (existingPlayer) {
        console.log(`Updating player: ${player.nickname}`)
        // Update existing player
        await client
          .patch(existingPlayer._id)
          .set({
            name: player.name,
            elo: player.elo,
          })
          .commit()
        console.log(`Successfully updated player: ${player.nickname}`)
      } else {
        console.log(`Creating new player: ${player.nickname}`)
        // Create new player
        const created = await client.create({
          _type: 'player',
          ...player,
        })
        console.log(`Successfully created player: ${player.nickname}`, created)
      }
    } catch (error) {
      console.error(`Error processing player ${player.nickname}:`, error.message)
      if (error.response) {
        console.error('Response details:', {
          statusCode: error.response.statusCode,
          statusMessage: error.response.statusMessage,
          body: error.response.body,
        })
      }
    }
  }

  console.log('\nImport completed!')
}

// Run the import
importPlayers().catch(error => {
  console.error('Fatal error:', error)
  process.exit(1)
}) 