import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/router'
import Image from 'next/image'
import { urlFor, sanity } from '../lib/sanity'
import { useLanguage } from '../lib/LanguageContext'
import type { SanityImageSource } from '@sanity/image-url/lib/types/types'
import type { PortableTextBlock } from '@portabletext/types'

// Utility function to extract plain text from PortableText
function extractPlainText(portableText: PortableTextBlock[] | string): string {
  if (typeof portableText === 'string') {
    return portableText
  }

  if (!Array.isArray(portableText)) {
    return ''
  }

  return portableText
    .map(block => {
      if (block._type === 'block' && block.children) {
        return block.children
          .map(child => child.text || '')
          .join('')
      }
      return ''
    })
    .join(' ')
    .trim()
}

interface CarouselItem {
  _id: string
  title: string
  type: 'news' | 'tournament'
  date: string
  image?: SanityImageSource | null
  video?: {
    _type: string
    asset?: {
      _ref: string
      _type: string
    }
  } | null
  // News specific
  summary?: PortableTextBlock[] | string
  publishedAt?: string
  // Tournament specific
  name?: string
  qualDate?: string
  status?: string
}

interface HighlightsCarouselProps {
  items: CarouselItem[]
}

export default function HighlightsCarousel({ items }: HighlightsCarouselProps) {
  const { t, language } = useLanguage()
  const router = useRouter()
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying || items.length <= 1) return

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % items.length)
    }, 5000) // Change slide every 5 seconds

    return () => clearInterval(interval)
  }, [isAutoPlaying, items.length])

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'ArrowLeft') {
        goToPrevious()
      } else if (event.key === 'ArrowRight') {
        goToNext()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [])

  // Touch handling for mobile swipe
  const minSwipeDistance = 50

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return
    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > minSwipeDistance
    const isRightSwipe = distance < -minSwipeDistance

    if (isLeftSwipe) {
      goToNext()
    } else if (isRightSwipe) {
      goToPrevious()
    }
  }

  const goToPrevious = useCallback(() => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? items.length - 1 : prevIndex - 1
    )
  }, [items.length])

  const goToNext = useCallback(() => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % items.length)
  }, [items.length])

  const goToSlide = useCallback((index: number) => {
    setCurrentIndex(index)
  }, [])

  const handleItemClick = useCallback((item: CarouselItem) => {
    if (item.type === 'news') {
      router.push(`/news?open=${item._id}`)
    } else {
      router.push(`/tournaments?open=${item._id}`)
    }
  }, [router])

  const getImageUrl = useCallback((image: SanityImageSource | undefined | null) => {
    if (!image) return null

    // Check if it's a file asset (video) - these have _ref starting with 'file-'
    if (typeof image === 'object' && 'asset' in image && image.asset && '_ref' in image.asset) {
      if (image.asset._ref.startsWith('file-')) {
        return null // Don't try to use urlFor on file assets
      }
    }

    try {
      return urlFor(image).width(1200).height(500).url()
    } catch (error) {
      console.error('Error generating image URL:', error)
      return null
    }
  }, [])

  const getVideoUrl = useCallback((video: CarouselItem['video']) => {
    if (!video || !video.asset || !video.asset._ref) return null

    const assetRef = video.asset._ref
    const parts = assetRef.split('-')
    if (parts.length !== 3 || parts[0] !== 'file') {
      return null
    }

    const projectId = sanity.config().projectId
    const dataset = sanity.config().dataset
    const assetId = parts[1]
    const extension = parts[2]

    if (!projectId || !dataset || !assetId || !extension) {
      return null
    }

    return `https://cdn.sanity.io/files/${projectId}/${dataset}/${assetId}.${extension}`
  }, [])

  const formatDate = useCallback((dateString: string | null | undefined) => {
    if (!dateString) return 'TBA'
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return 'TBA'
      
      if (language === 'ru') {
        return date.toLocaleDateString('ru-RU', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        })
      } else {
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        })
      }
    } catch (error) {
      console.error('Error formatting date:', error)
      return 'TBA'
    }
  }, [language])

  if (!items || items.length === 0) {
    return null
  }

  // Ensure currentIndex is within bounds
  const safeCurrentIndex = Math.max(0, Math.min(currentIndex, items.length - 1))
  const currentItem = items[safeCurrentIndex]

  // Update currentIndex if it was out of bounds
  if (safeCurrentIndex !== currentIndex) {
    setCurrentIndex(safeCurrentIndex)
  }

  return (
    <section className="w-full py-16">
      <div className="max-w-7xl mx-auto px-4">
        {/* Section Title */}
        <div className="flex items-center mb-8">
          <h2 className="text-3xl font-bold mr-4">{t('highlights_title')}</h2>
          <div className="flex-1 h-1 bg-gradient-to-r from-blue-700 via-purple-700 to-cyan-700 rounded-full opacity-40" />
        </div>

        {/* Carousel Container */}
        <div
          className="relative w-full h-96 md:h-[500px] rounded-2xl overflow-hidden group"
          onMouseEnter={() => setIsAutoPlaying(false)}
          onMouseLeave={() => setIsAutoPlaying(true)}
          onTouchStart={onTouchStart}
          onTouchMove={onTouchMove}
          onTouchEnd={onTouchEnd}
        >
          {/* Background Media */}
          <div className="absolute inset-0">
            {(() => {
              const videoUrl = getVideoUrl(currentItem.video)
              const imageUrl = getImageUrl(currentItem.image)

              if (videoUrl) {
                return (
                  <video
                    src={videoUrl}
                    autoPlay
                    muted
                    loop
                    playsInline
                    preload="metadata"
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      console.error('Video failed to load:', e)
                      console.error('Video URL:', videoUrl)
                      console.error('Video element:', e.target)
                    }}
                    onLoadStart={() => {
                      console.log('Video loading started:', videoUrl)
                    }}
                    onCanPlay={() => {
                      console.log('Video can play:', videoUrl)
                    }}
                    onLoadedData={() => {
                      console.log('Video loaded data:', videoUrl)
                    }}
                  >
                    Your browser does not support the video tag.
                  </video>
                )
              } else if (imageUrl) {
                return (
                  <Image
                    src={imageUrl}
                    alt={currentItem.title || currentItem.name || ''}
                    fill
                    className="object-cover"
                    priority
                  />
                )
              } else {
                return (
                  <div className="w-full h-full bg-gradient-to-br from-zinc-800 via-zinc-700 to-zinc-600" />
                )
              }
            })()}
            {/* Overlay */}
            <div className="absolute inset-0 bg-black/50" />
          </div>

          {/* Content */}
          <div 
            className="relative z-10 h-full flex items-end p-8 cursor-pointer"
            onClick={() => handleItemClick(currentItem)}
          >
            <div className="w-full">
              <div className="mb-4">
                <span className={`inline-block px-3 py-1 rounded-full text-sm font-semibold ${
                  currentItem.type === 'news' 
                    ? 'bg-blue-600 text-blue-100' 
                    : 'bg-green-600 text-green-100'
                }`}>
                  {currentItem.type === 'news' ? t('news') : t('tournaments')}
                </span>
              </div>
              <h3 className="text-2xl md:text-4xl font-bold text-white mb-4 leading-tight">
                {currentItem.title || currentItem.name}
              </h3>
              {currentItem.summary && (
                <p className="text-gray-200 text-lg mb-4 max-w-2xl">
                  {extractPlainText(currentItem.summary)}
                </p>
              )}
              <div className="text-gray-300">
                {formatDate(currentItem.date)}
              </div>
            </div>
          </div>

          {/* Navigation Arrows */}
          {items.length > 1 && (
            <>
              <button
                onClick={goToPrevious}
                className="absolute left-4 top-1/2 -translate-y-1/2 z-20 p-3 rounded-full bg-black/30 hover:bg-black/50 text-white transition-all duration-200 opacity-0 group-hover:opacity-100"
                aria-label={t('highlights_previous')}
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <button
                onClick={goToNext}
                className="absolute right-4 top-1/2 -translate-y-1/2 z-20 p-3 rounded-full bg-black/30 hover:bg-black/50 text-white transition-all duration-200 opacity-0 group-hover:opacity-100"
                aria-label={t('highlights_next')}
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </>
          )}
        </div>

        {/* Slide Indicators */}
        {items.length > 1 && (
          <div className="flex justify-center mt-6 space-x-2">
            {items.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-200 ${
                  index === safeCurrentIndex
                    ? 'bg-blue-500 scale-110'
                    : 'bg-gray-500 hover:bg-gray-400'
                }`}
                aria-label={`${t('highlights_slide_indicator')} ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>
    </section>
  )
}
