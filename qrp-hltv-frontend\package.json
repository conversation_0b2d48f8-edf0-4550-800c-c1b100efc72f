{"name": "qrp-hltv-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "export": "next export", "start": "next start", "lint": "next lint"}, "dependencies": {"@portabletext/react": "^3.2.1", "@sanity/client": "^7.3.0", "@sanity/image-url": "^1.1.0", "autoprefixer": "^10.4.21", "csv-parse": "^5.6.0", "flag-icons": "^7.3.2", "next": "13.4.9", "react": "^18.2.0", "react-dom": "^18.2.0", "styled-jsx": "^5.1.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/glob": "^8.1.0", "@types/node": "^20.17.50", "@types/react": "^18.3.22", "@types/react-dom": "^18", "chalk": "^5.4.1", "eslint": "^8.56.0", "eslint-config-next": "13.4.9", "glob": "^11.0.2", "postcss": "^8.4.35", "tailwindcss": "^3.4.17", "tsx": "^4.19.4", "typescript": "^5"}}