export default {
  name: 'tournament',
  title: 'Tournament',
  type: 'document',
  fields: [
    { name: 'name', type: 'string', title: 'Tournament Name' },
    { name: 'qualDate', type: 'datetime', title: 'Qualifiers Date' },
    { name: 'tandemsDate', type: 'datetime', title: 'Tandems Date' },
    { name: 'status', type: 'string', title: 'Status', options: { list: ['Upcoming', 'Ongoing', 'Finished'] } },
    {
      name: 'photo',
      title: 'Photo',
      type: 'image',
      options: {
        hotspot: true,
      }
    },
    {
      name: 'video',
      title: 'Video',
      type: 'file',
      description: 'For best performance, keep video files under 5MB. Use WebM or MP4 format with H.264 encoding.',
      options: {
        accept: 'video/*'
      },
      validation: (Rule) => Rule.custom((file) => {
        if (!file) return true // Video is optional

        // Note: File size validation in Sanity is limited as the file object
        // doesn't contain size information at validation time
        return true
      })
    },
    { name: 'description', type: 'array', title: 'Description', of: [{ type: 'block' }] }
  ]
}
