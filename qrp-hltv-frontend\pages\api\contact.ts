import type { NextApiRequest, NextApiResponse } from 'next'
import { sanityWrite } from '../../lib/sanity'

interface ContactFormData {
  name: string
  discord: string
  subject: string
  message: string
}

// Helper function to convert string to rich text format
function stringToPortableText(text: string) {
  return [
    {
      _type: 'block',
      _key: Math.random().toString(36).substr(2, 9),
      style: 'normal',
      markDefs: [],
      children: [
        {
          _type: 'span',
          _key: Math.random().toString(36).substr(2, 9),
          text: text,
          marks: []
        }
      ]
    }
  ]
}

interface ValidationError {
  field: string
  message: string
}

function validateContactForm(data: any): { isValid: boolean; errors: ValidationError[] } {
  const errors: ValidationError[] = []

  if (!data.name || typeof data.name !== 'string' || !data.name.trim()) {
    errors.push({ field: 'name', message: 'Name is required' })
  }

  if (!data.discord || typeof data.discord !== 'string' || !data.discord.trim()) {
    errors.push({ field: 'discord', message: 'Discord tag is required' })
  } else if (!/^@[a-z0-9._]{2,32}$/.test(data.discord)) {
    errors.push({ field: 'discord', message: 'Please enter a valid Discord tag (e.g., @username)' })
  }

  if (!data.subject || typeof data.subject !== 'string' || !data.subject.trim()) {
    errors.push({ field: 'subject', message: 'Subject is required' })
  }

  if (!data.message || typeof data.message !== 'string' || !data.message.trim()) {
    errors.push({ field: 'message', message: 'Message is required' })
  } else if (data.message.trim().length < 10) {
    errors.push({ field: 'message', message: 'Message must be at least 10 characters long' })
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const timestamp = new Date().toISOString()
  console.log(`CONTACT API: ${req.method} request received at ${timestamp}`)

  // Only allow POST requests
  if (req.method !== 'POST') {
    console.warn(`CONTACT API: Method ${req.method} not allowed`)
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    })
  }

  try {
    // Validate request body
    const validation = validateContactForm(req.body)

    if (!validation.isValid) {
      console.warn('CONTACT API: Validation failed:', validation.errors)
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validation.errors
      })
    }

    const formData: ContactFormData = {
      name: req.body.name.trim(),
      discord: req.body.discord.trim(),
      subject: req.body.subject.trim(),
      message: req.body.message.trim()
    }

    console.log(`CONTACT API: Processing submission from ${formData.discord}`)

    // Create support request document in Sanity
    const supportRequest = {
      _type: 'supportRequest',
      name: formData.name,
      discord: formData.discord,
      subject: formData.subject,
      message: stringToPortableText(formData.message),
      status: 'new',
      priority: 'medium',
      submittedAt: timestamp,
    }

    const result = await sanityWrite.create(supportRequest)

    console.log(`CONTACT API: Support request created with ID: ${result._id}`)

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Support request submitted successfully',
      requestId: result._id
    })

  } catch (error) {
    console.error('CONTACT API: Error processing request:', error)

    let errorMessage = 'Internal server error. Please try again later.'
    let statusCode = 500

    // Check if it's a Sanity-specific error
    if (error instanceof Error) {
      console.error('CONTACT API: Error details:', {
        message: error.message,
        stack: error.stack
      })

      // Handle specific Sanity errors
      if (error.message.includes('Insufficient permissions')) {
        errorMessage = 'Configuration error: Unable to save your request. Please contact support directly.'
        console.error('CONTACT API: Sanity token lacks create permissions')
      } else if (error.message.includes('Invalid token')) {
        errorMessage = 'Configuration error: Authentication failed. Please contact support directly.'
        console.error('CONTACT API: Invalid Sanity token')
      } else if (error.message.includes('Project not found')) {
        errorMessage = 'Configuration error: Service unavailable. Please contact support directly.'
        console.error('CONTACT API: Sanity project not found')
      }
    }

    return res.status(statusCode).json({
      success: false,
      message: errorMessage,
      error: process.env.NODE_ENV === 'development' ? {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      } : undefined
    })
  }
}
