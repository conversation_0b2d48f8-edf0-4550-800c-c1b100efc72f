import type { NextApiRequest, NextApiResponse } from 'next'

// Simple in-memory cache to prevent duplicate revalidations
const revalidationCache = new Map<string, number>()
const RATE_LIMIT_WINDOW = 10000 // 10 seconds

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const timestamp = new Date().toISOString();
  console.log('='.repeat(50));
  console.log(`REVALIDATE FUNCTION HANDLER STARTED - ${timestamp}`);
  console.log(`Request method: ${req.method}`);
  console.log(`Request query: ${JSON.stringify(req.query)}`);
  console.log(`Request headers: ${JSON.stringify(req.headers, null, 2)}`);
  console.log(`Environment check:`);
  console.log(`  - NODE_ENV: ${process.env.NODE_ENV}`);
  console.log(`  - SANITY_WEBHOOK_SECRET exists: ${!!process.env.SANITY_WEBHOOK_SECRET}`);
  console.log(`  - SANITY_WEBHOOK_SECRET value: ${process.env.SANITY_WEBHOOK_SECRET ? process.env.SANITY_WEBHOOK_SECRET.substring(0, 5) + '...' : 'NOT SET'}`);

  // Only allow POST requests
  if (req.method !== 'POST') {
    console.warn('Method not allowed:', req.method);
    return res.status(405).json({ message: 'Method not allowed' })
  }

  // Check for secret to confirm this is a valid request
  if (req.query.secret !== process.env.SANITY_WEBHOOK_SECRET) {
    console.error('❌ Invalid webhook secret provided.');
    console.error(`Received secret: "${req.query.secret}"`);
    console.error(`Expected secret: "${process.env.SANITY_WEBHOOK_SECRET}"`);
    console.error(`Secret comparison: ${req.query.secret} === ${process.env.SANITY_WEBHOOK_SECRET} = ${req.query.secret === process.env.SANITY_WEBHOOK_SECRET}`);
    return res.status(401).json({
      message: 'Invalid token',
      receivedSecret: req.query.secret ? 'provided' : 'missing',
      expectedSecret: process.env.SANITY_WEBHOOK_SECRET ? 'configured' : 'missing'
    })
  }
  console.log('✅ Webhook secret validated successfully.');

  try {
    // Get document type and action from webhook payload
    const documentType = req.body?._type;
    const action = req.body?.action || 'update';
    const rawPathData = req.body?.path;

    console.log('Webhook payload:', {
      documentType,
      action,
      rawPathData,
      fullBody: JSON.stringify(req.body, null, 2)
    });

    let pathsToProcess: string[] = [];

    // If we have document type, determine paths automatically
    if (documentType) {
      console.log(`Document type detected: ${documentType}`);

      switch (documentType) {
        case 'player':
          pathsToProcess = ['/players/', '/teams/', '/'];
          console.log('👤 Player document detected - will revalidate players, teams, and homepage');
          break;
        case 'team':
          pathsToProcess = ['/teams/', '/players/', '/'];
          break;
        case 'news':
          pathsToProcess = ['/news/', '/'];
          break;
        case 'tournament':
          pathsToProcess = ['/tournaments/', '/'];
          break;
        case 'partner':
          pathsToProcess = ['/partners/', '/'];
          console.log('🤝 Partner document detected - will revalidate partners page and homepage');
          break;
        default:
          console.log(`Unknown document type: ${documentType}, falling back to path data`);
      }
    }

    // Fallback to path data if no document type or no paths determined
    if (pathsToProcess.length === 0) {
      if (rawPathData === null || typeof rawPathData === 'undefined') {
        console.log('No specific path to revalidate and no document type. Webhook processed.');
        return res.json({ revalidated: false, message: 'No specific path to revalidate for this update.', details: [] });
      } else if (Array.isArray(rawPathData)) {
        pathsToProcess = rawPathData.map(p => String(p || '').trim()).filter(p => p !== '');
      } else if (typeof rawPathData === 'string' && rawPathData.trim() !== '') {
        pathsToProcess = [rawPathData.trim()];
      } else {
        console.log('Path data is not a valid string, array, or null. Webhook processed. Raw path data:', rawPathData);
        return res.json({ revalidated: false, message: 'Path data is not a valid string, array, or null.', details: [] });
      }
    }

    if (pathsToProcess.length === 0) {
      console.log('No valid paths to process after all processing. Raw path data:', rawPathData);
      return res.json({ revalidated: false, message: 'No valid paths to process from the provided data.', details: [] });
    }

    console.log('Paths to revalidate:', pathsToProcess);

    const revalidationDetails: Array<{path: string, status: string, reason?: string}> = [];
    let overallRevalidationAttempted = false;
    let overallRevalidationSuccess = false;

    for (const singlePath of pathsToProcess) {
      let cleanedPath = singlePath; // Already trimmed in the processing step above
      if (cleanedPath.endsWith(',')) {
        cleanedPath = cleanedPath.slice(0, -1).trimEnd();
      }

      if (!cleanedPath) {
        console.warn('Skipping empty path after final cleaning. Original element from pathsToProcess:', singlePath);
        revalidationDetails.push({ path: singlePath, status: 'skipped_empty_after_cleaning' });
        continue;
      }

      overallRevalidationAttempted = true;
      const now = Date.now();
      const lastRevalidation = revalidationCache.get(cleanedPath);

      if (lastRevalidation && (now - lastRevalidation) < RATE_LIMIT_WINDOW) {
        console.log(`Rate limited: ${cleanedPath} (last revalidated ${now - lastRevalidation}ms ago)`);
        revalidationDetails.push({ path: cleanedPath, status: 'rate_limited' });
        continue;
      }

      try {
        console.log(`Attempting to revalidate cleaned path: ${cleanedPath}`);

        // Check if res.revalidate exists (it might not on all Netlify deployments)
        if (typeof res.revalidate === 'function') {
          await res.revalidate(cleanedPath);
          revalidationCache.set(cleanedPath, now);
          console.log(`✅ Successfully revalidated: ${cleanedPath}`);
          revalidationDetails.push({ path: cleanedPath, status: 'success' });
          overallRevalidationSuccess = true;
        } else {
          console.warn(`⚠️ res.revalidate is not available. Type: ${typeof res.revalidate}`);
          console.warn('This might be a Netlify/Next.js compatibility issue.');
          console.warn('Attempting fallback: triggering rebuild webhook if available...');

          // Fallback: trigger a rebuild if ISR is not available
          const buildHookUrl = process.env.NETLIFY_BUILD_HOOK_URL;
          if (buildHookUrl && documentType === 'partner') {
            try {
              console.log('🔄 Triggering fallback rebuild for partner update...');
              const rebuildResponse = await fetch(buildHookUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  trigger: 'sanity-webhook-fallback',
                  documentType,
                  path: cleanedPath,
                  timestamp: new Date().toISOString()
                })
              });

              if (rebuildResponse.ok) {
                console.log('✅ Fallback rebuild triggered successfully');
                revalidationDetails.push({
                  path: cleanedPath,
                  status: 'fallback_rebuild_triggered',
                  reason: 'ISR not available, triggered full rebuild'
                });
                overallRevalidationSuccess = true;
              } else {
                console.error('❌ Fallback rebuild failed:', rebuildResponse.status);
                revalidationDetails.push({
                  path: cleanedPath,
                  status: 'error',
                  reason: 'res.revalidate not available and fallback rebuild failed'
                });
              }
            } catch (rebuildError) {
              console.error('❌ Fallback rebuild error:', rebuildError);
              revalidationDetails.push({
                path: cleanedPath,
                status: 'error',
                reason: 'res.revalidate not available and fallback rebuild error'
              });
            }
          } else {
            revalidationDetails.push({
              path: cleanedPath,
              status: 'error',
              reason: 'res.revalidate function not available - possible Netlify/Next.js compatibility issue'
            });
          }
        }
      } catch (revalError) {
        let errorMessage = 'Error revalidating path';
        if (revalError instanceof Error) {
          errorMessage = revalError.message;
        }
        console.error(`❌ Failed to revalidate ${cleanedPath}:`, errorMessage);
        console.error('Full error:', revalError);
        revalidationDetails.push({ path: cleanedPath, status: 'error', reason: errorMessage });
      }
    }

    if (revalidationCache.size > 100) {
      const cutoff = Date.now() - RATE_LIMIT_WINDOW;
      for (const [path, timestamp] of revalidationCache.entries()) {
        if (timestamp < cutoff) {
          revalidationCache.delete(path);
        }
      }
    }

    if (!overallRevalidationAttempted && pathsToProcess.length > 0) {
         // This case might happen if all paths were empty after cleaning but not initially empty.
         console.log('All paths resulted in empty strings after cleaning, no revalidation attempted.');
         return res.json({ revalidated: false, message: 'All paths were empty after cleaning.', details: revalidationDetails });
    }

    return res.json({
      revalidated: overallRevalidationSuccess,
      message: overallRevalidationSuccess ? 'Revalidation process completed.' : 'Revalidation process completed with no successful revalidations.',
      details: revalidationDetails
    });

  } catch (err) {
    let errorMessage = 'An unexpected error occurred in the revalidate handler';
    if (err instanceof Error) {
      errorMessage = err.message;
    }
    console.error('Revalidation handler main error:', errorMessage, err);
    return res.status(500).json({ message: errorMessage, revalidated: false, details: [] });
  }
}