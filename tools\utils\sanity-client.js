/**
 * Shared Sanity Client Configuration
 * 
 * Provides a standardized Sanity client setup with environment validation
 * and connection testing for all maintenance tools.
 */

const { createClient } = require('@sanity/client')
require('dotenv').config()

/**
 * Validates required environment variables
 * @throws {Error} If required environment variables are missing
 */
function validateEnvironment() {
  const required = [
    'NEXT_PUBLIC_SANITY_PROJECT_ID',
    'NEXT_PUBLIC_SANITY_DATASET', 
    'SANITY_TOKEN'
  ]
  
  const missing = required.filter(key => !process.env[key])
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
  }
}

/**
 * Creates a configured Sanity client
 * @returns {Object} Configured Sanity client
 */
function createSanityClient() {
  validateEnvironment()
  
  return createClient({
    projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
    dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
    token: process.env.SANITY_TOKEN,
    apiVersion: '2024-03-24',
    useCdn: false,
  })
}

/**
 * Tests the Sanity connection and permissions
 * @param {Object} client - Sanity client instance
 * @returns {Promise<boolean>} True if connection is successful
 */
async function testConnection(client) {
  try {
    console.log('🔗 Testing Sanity connection...')
    const result = await client.fetch('*[_type == "player"][0]')
    console.log('✅ Sanity connection verified')
    return true
  } catch (error) {
    console.error('❌ Failed to connect to Sanity:', error.message)
    return false
  }
}

/**
 * Creates a Sanity client and tests the connection
 * @returns {Promise<Object>} Configured and tested Sanity client
 * @throws {Error} If connection fails
 */
async function createAndTestClient() {
  const client = createSanityClient()
  const connectionValid = await testConnection(client)
  
  if (!connectionValid) {
    throw new Error('Failed to establish connection to Sanity. Please check your credentials.')
  }
  
  return client
}

module.exports = {
  validateEnvironment,
  createSanityClient,
  testConnection,
  createAndTestClient
}
