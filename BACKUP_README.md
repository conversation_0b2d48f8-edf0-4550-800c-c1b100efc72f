# 🔄 Automated CMS Backup System

This repository includes a comprehensive automated backup system for your Sanity CMS data. Your content is automatically backed up daily and stored safely in your GitHub repository.

## 🚀 Features

- **Daily Automated Backups** - Runs every day at 2 AM UTC
- **Weekly Full Backups** - Complete backups including drafts every Sunday
- **Manual Backup Triggers** - Run backups anytime via GitHub Actions
- **Easy Restoration** - Simple restore process from any backup
- **Automatic Cleanup** - Old backups are automatically removed
- **Backup Verification** - Each backup includes a manifest with details

## 📁 Backup Structure

```
backups/
├── daily/           # Daily backups (published content only)
│   ├── 2024-01-15/
│   ├── 2024-01-16/
│   └── ...
└── weekly/          # Weekly backups (full including drafts)
    ├── 2024-W03/
    ├── 2024-W04/
    └── ...
```

Each backup contains:
- `player.json` - All player data
- `team.json` - All team data  
- `news.json` - All news articles
- `tournament.json` - All tournament data
- `partner.json` - All partner information
- `supportRequest.json` - All support requests
- `assets.json` - Asset metadata
- `manifest.json` - Backup details and verification

## 🛠️ Manual Commands

### Run a Backup
```bash
# Backup published content only
npm run backup

# Backup everything including drafts
npm run backup-full
```

### Restore from Backup
```bash
npm run restore
```

The restore command will:
1. Show you all available backups
2. Let you select which backup to restore
3. Ask if you want to overwrite existing content
4. Safely restore your data

## ⚙️ GitHub Actions Setup

### Required Secrets

Add these secrets to your GitHub repository settings:

1. Go to your repository on GitHub
2. Click **Settings** → **Secrets and variables** → **Actions**
3. Add these repository secrets:

```
SANITY_PROJECT_ID=al3wd5y8
SANITY_DATASET=production
SANITY_TOKEN=your-sanity-token-here
```

### Backup Schedules

- **Daily Backup**: Every day at 2 AM UTC (published content only)
- **Weekly Backup**: Every Sunday at 3 AM UTC (full backup including drafts)

### Manual Triggers

You can manually trigger backups:

1. Go to **Actions** tab in your GitHub repository
2. Select "Automated CMS Backup" or "Weekly Full Backup"
3. Click "Run workflow"
4. Choose backup type (for daily backup)

## 🔍 Monitoring Backups

### Check Backup Status

1. Go to **Actions** tab in your repository
2. Look for green checkmarks ✅ on backup workflows
3. Click on any workflow run to see detailed logs

### Backup Notifications

- ✅ Successful backups show a summary in the workflow
- ❌ Failed backups will show error details
- 📊 Each backup includes document counts and status

## 🚨 Emergency Recovery

If your CMS gets corrupted or accidentally deleted:

### Quick Recovery Steps

1. **Clone your repository** (if not already local):
   ```bash
   git clone https://github.com/your-username/carx-hltv.git
   cd carx-hltv
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Run restore**:
   ```bash
   npm run restore
   ```

4. **Select the most recent backup** and follow prompts

5. **Verify restoration** by checking your CMS

### Recovery from Specific Date

If you need to restore from a specific date:

1. Look in `backups/daily/` for the date you want
2. Run `npm run restore`
3. Select the backup from that date

## 📋 Backup Contents

Your backups include ALL of your CMS data:

### Content Types
- **Players** (names, ELO, teams, countries, etc.)
- **Teams** (team info, player rosters, ELO)
- **News** (articles, images, publish dates)
- **Tournaments** (events, dates, status, media)
- **Partners** (partner info, descriptions, logos)
- **Support Requests** (user submissions, status, notes)

### Assets
- Image metadata and references
- File metadata and references
- Asset URLs and properties

## 🔧 Troubleshooting

### Backup Failed
1. Check GitHub Actions logs for error details
2. Verify Sanity credentials in repository secrets
3. Test connection with: `node tools/backup.js`

### Restore Failed
1. Ensure you have write permissions to Sanity
2. Check that backup files exist and are valid
3. Verify your Sanity token has write access

### Missing Backups
1. Check if GitHub Actions are enabled
2. Verify repository secrets are set correctly
3. Look for failed workflow runs in Actions tab

## 🛡️ Security

- Backup files are stored in your private GitHub repository
- Sanity tokens are stored as encrypted GitHub secrets
- No sensitive data is exposed in logs or public areas
- Backups include only data structure, not authentication tokens

## 📞 Support

If you need help with the backup system:

1. Check the GitHub Actions logs first
2. Look for error messages in the workflow runs
3. Test manual backup: `npm run backup`
4. Test manual restore: `npm run restore`

Your data is safe! 🛡️ The automated backup system ensures you always have recent copies of your CMS content.
