import type { NextApiRequest, NextApiResponse } from 'next'

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const health = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    version: '1.0.0',
    checks: {
      sanityWebhookSecret: !!process.env.SANITY_WEBHOOK_SECRET,
      sanityProjectId: !!process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
      sanityDataset: !!process.env.NEXT_PUBLIC_SANITY_DATASET,
      sanityToken: !!process.env.SANITY_TOKEN
    },
    endpoints: {
      revalidate: '/api/revalidate',
      testRevalidate: '/api/test-revalidate',
      health: '/api/health'
    }
  };

  res.status(200).json(health);
}
