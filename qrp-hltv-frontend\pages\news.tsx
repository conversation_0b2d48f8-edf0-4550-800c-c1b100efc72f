import { sanity } from '../lib/sanity'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { PortableText } from '@portabletext/react'
import { urlFor } from '../lib/sanity'
import Link from 'next/link'
import Image from 'next/image'
import Head from 'next/head'
import type { PortableTextBlock } from '@portabletext/types'
import type { SanityImageSource } from '@sanity/image-url/lib/types/types'
import { useLanguage } from '../lib/LanguageContext'
import { getLocalizedString, getLocalizedRichText } from '../lib/multilingual'

interface News {
  _id: string;
  title: string;
  image?: SanityImageSource;
  publishedAt: string;
  body?: PortableTextBlock[];
}

export async function getStaticProps() {
  const news = await sanity.fetch(`*[_type == "news" && !(_id in path("drafts.**"))] | order(publishedAt desc){
    _id, title, en_title, image, publishedAt, body, en_body
  }`)
  return {
    props: {
      news,
      lastUpdated: new Date().toISOString() // Add timestamp to force cache invalidation
    },
    revalidate: 60,
  }
}

export default function NewsPage({ news }: { news: News[] }) {
  const router = useRouter()
  const [selectedNews, setSelectedNews] = useState<News | null>(null)
  const { t, language } = useLanguage()

  // Handle query parameter to auto-open news modal
  useEffect(() => {
    if (router.isReady && router.query.open && news.length > 0) {
      const newsId = router.query.open as string;
      const newsItem = news.find(n => n._id === newsId);
      if (newsItem) {
        setSelectedNews(newsItem);
        // Clean up URL without triggering navigation
        router.replace('/news', undefined, { shallow: true });
      }
    }
  }, [router.isReady, router.query.open, news])

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'TBA'
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return 'TBA'
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'UTC'
      })
    } catch (error) {
      console.error('Error formatting date:', error)
      return 'TBA'
    }
  }

  return (
    <>
      <Head>
        <title>{`${t('news_page_title')} - QRP HLTV`}</title>
        <meta name="description" content="Latest news and updates from CarX Drift Racing tournaments and events" />
        <meta property="og:title" content={`${t('news_page_title')} - QRP HLTV`} />
        <meta property="og:description" content="Latest news and updates from CarX Drift Racing tournaments and events" />
        <meta property="og:url" content="https://qrp-hltv.com/news" />
        <meta name="twitter:title" content={`${t('news_page_title')} - QRP HLTV`} />
        <meta name="twitter:description" content="Latest news and updates from CarX Drift Racing tournaments and events" />
      </Head>
      <main className="max-w-4xl mx-auto py-12 px-4">
        <div className="flex items-center mb-8">
          <h1 className="text-3xl font-bold mr-4">{t('news_page_title')}</h1>
          <div className="flex-1 h-1 bg-blue-700 rounded-full opacity-40" />
        </div>
        <div className="grid gap-6 md:grid-cols-2">
          {news.map((n: News) => (
            <div 
              key={n._id} 
              className="p-5 rounded-2xl bg-zinc-900 border border-zinc-800 shadow-md hover:shadow-blue-900/40 hover:border-blue-700 transition cursor-pointer group"
              onClick={() => setSelectedNews(n)}
            >
              <div className="flex items-start space-x-4"> {/* Main flex container for text + image */}
                <div className="flex-1"> {/* Container for text elements */}
                  <div className="text-xl font-bold group-hover:text-blue-400 transition">{getLocalizedString(n, 'title', language)}</div>
                  <div className="text-xs text-gray-500 mt-2">{formatDate(n.publishedAt)}</div>
                </div>
                {n.image && (
                  <div className="w-36 h-24 flex-shrink-0"> {/* Container for the image, adjust w-36 (144px) and h-24 (96px) as needed for aspect ratio and size */}
                    <Image
                      src={urlFor(n.image).width(150).height(100).url()} // Using 150x100 for the image source URL
                      alt={n.title || 'News image'} // Added a fallback for alt text
                      width={150} // next/image width property
                      height={100} // next/image height property
                      className="rounded-md object-cover w-full h-full" // Make image fill its container
                    />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* News Modal */}
        {selectedNews && (
          <div
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setSelectedNews(null)}
          >
            <div
              className="bg-zinc-900 rounded-2xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto border border-zinc-700"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-start mb-4">
                <h2 className="text-2xl font-bold text-blue-400">{getLocalizedString(selectedNews, 'title', language)}</h2>
                <button 
                  onClick={() => setSelectedNews(null)}
                  className="text-zinc-400 hover:text-zinc-100 transition"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div className="space-y-4">
                {selectedNews.image && (
                  <Image
                    src={urlFor(selectedNews.image).width(600).url()}
                    alt={getLocalizedString(selectedNews, 'title', language)}
                    width={600}
                    height={300}
                    className="rounded-lg w-full object-cover mb-4 max-h-72"
                  />
                )}
                <div className="text-sm text-gray-400 mb-2">{formatDate(selectedNews.publishedAt)}</div>
                {getLocalizedRichText(selectedNews, 'body', language).length > 0 && (
                  <div className="mt-4 prose prose-invert max-w-none">
                    <PortableText value={getLocalizedRichText(selectedNews, 'body', language)} />
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </main>
    </>
  )
} 