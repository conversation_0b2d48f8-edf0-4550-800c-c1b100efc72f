import type { NextApiRequest, NextApiResponse } from 'next'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  console.log('🔧 MANUAL REVALIDATE ENDPOINT CALLED');
  console.log('Method:', req.method);
  console.log('Query:', JSON.stringify(req.query));

  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Check for secret to confirm this is a valid request
  if (req.query.secret !== process.env.SANITY_WEBHOOK_SECRET) {
    console.error('❌ Invalid webhook secret provided.');
    return res.status(401).json({
      message: 'Invalid token',
      receivedSecret: req.query.secret ? 'provided' : 'missing',
      expectedSecret: process.env.SANITY_WEBHOOK_SECRET ? 'configured' : 'missing'
    });
  }

  try {
    const pathsToRevalidate = ['/partners/', '/players/', '/teams/', '/tournaments/', '/news/', '/'];
    const results = [];

    console.log('🔄 Starting manual revalidation of all pages...');

    for (const path of pathsToRevalidate) {
      try {
        console.log(`Revalidating: ${path}`);
        
        if (typeof res.revalidate === 'function') {
          await res.revalidate(path);
          console.log(`✅ Successfully revalidated: ${path}`);
          results.push({ path, status: 'success' });
        } else {
          console.warn(`⚠️ res.revalidate not available for ${path}`);
          results.push({ 
            path, 
            status: 'error', 
            reason: 'res.revalidate function not available' 
          });
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error(`❌ Failed to revalidate ${path}:`, errorMessage);
        results.push({ path, status: 'error', reason: errorMessage });
      }
    }

    const successCount = results.filter(r => r.status === 'success').length;
    const totalCount = results.length;

    return res.json({
      success: successCount > 0,
      message: `Manual revalidation completed: ${successCount}/${totalCount} paths successful`,
      results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Manual revalidation error:', error);
    return res.status(500).json({
      success: false,
      message: 'Manual revalidation failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
