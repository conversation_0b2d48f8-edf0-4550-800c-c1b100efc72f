import { createClient } from '@sanity/client'
import imageUrlBuilder from '@sanity/image-url'
import type { SanityImageSource } from '@sanity/image-url/lib/types/types'

// Read-only client for fetching published data
export const sanity = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || process.env.SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || process.env.SANITY_DATASET,
  apiVersion: '2023-05-24',
  useCdn: false, // Always fetch fresh data, never use CDN cache
  token: process.env.SANITY_TOKEN,
  perspective: 'published', // Only fetch published documents
  stega: false // Disable stega encoding for production
})

// Write client for creating/updating documents (server-side only)
export const sanityWrite = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || process.env.SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || process.env.SANITY_DATASET,
  apiVersion: '2023-05-24',
  useCdn: false,
  token: process.env.SANITY_TOKEN,
  perspective: 'raw', // Allow creating drafts and published documents
  stega: false
})

export const urlFor = (source: SanityImageSource) =>
  imageUrlBuilder(sanity).image(source)
