/**
 * Backup Tool for CARX HLTV
 * 
 * Creates automated backups of all Sanity CMS content with organized storage.
 * 
 * Usage: 
 * npm run backup (published content only)
 * npm run backup-full (includes drafts)
 */

const { createAndTestClient } = require('../utils/sanity-client')
const { logToolStart, logToolEnd, logProgress, logSuccess, logError, logStats } = require('../utils/logger')
const fs = require('fs')
const path = require('path')

// Content types to backup
const CONTENT_TYPES = [
  'player',
  'team', 
  'news',
  'tournament',
  'partner',
  'supportRequest'
]

function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true })
    console.log(`📁 Created directory: ${dirPath}`)
  }
}

function getBackupPaths(includeDrafts = false) {
  const now = new Date()
  const dateStr = now.toISOString().split('T')[0] // YYYY-MM-DD
  const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-') // HH-MM-SS
  
  // Calculate week number
  const startOfYear = new Date(now.getFullYear(), 0, 1)
  const weekNumber = Math.ceil(((now - startOfYear) / 86400000 + startOfYear.getDay() + 1) / 7)
  const weekStr = `${now.getFullYear()}-W${weekNumber.toString().padStart(2, '0')}`
  
  const backupType = includeDrafts ? 'full' : 'published'
  
  return {
    daily: path.join('backups', 'daily', dateStr),
    weekly: path.join('backups', 'weekly', weekStr),
    timestamped: path.join('backups', 'timestamped', `${dateStr}_${timeStr}_${backupType}`)
  }
}

async function backupContentType(client, contentType, includeDrafts = false) {
  const draftFilter = includeDrafts ? '' : ' && !(_id in path("drafts.**"))'
  const query = `*[_type == "${contentType}"${draftFilter}]`
  
  try {
    const documents = await client.fetch(query)
    console.log(`  📄 ${contentType}: ${documents.length} documents`)
    return documents
  } catch (error) {
    logError(`Failed to backup ${contentType}: ${error.message}`)
    return []
  }
}

async function saveBackupFiles(backupData, backupPaths) {
  const saves = []
  
  // Save to all backup locations
  for (const [location, dirPath] of Object.entries(backupPaths)) {
    ensureDirectoryExists(dirPath)
    
    for (const [contentType, documents] of Object.entries(backupData)) {
      const filePath = path.join(dirPath, `${contentType}.json`)
      saves.push({ location, contentType, filePath, count: documents.length })
      fs.writeFileSync(filePath, JSON.stringify(documents, null, 2))
    }
  }
  
  return saves
}

async function runBackup() {
  const includeDrafts = process.argv.includes('--full')
  
  try {
    logToolStart('SANITY CMS BACKUP', `${includeDrafts ? 'Full Backup (including drafts)' : 'Published Content Only'}`)
    
    // Initialize Sanity client and test connection
    const client = await createAndTestClient()
    
    // Get backup paths
    const backupPaths = getBackupPaths(includeDrafts)
    
    console.log('📁 Backup locations:')
    Object.entries(backupPaths).forEach(([type, path]) => {
      console.log(`  ${type}: ${path}`)
    })
    
    // Backup each content type
    console.log('\n📦 Backing up content types...')
    const backupData = {}
    let totalDocuments = 0
    
    for (let i = 0; i < CONTENT_TYPES.length; i++) {
      const contentType = CONTENT_TYPES[i]
      logProgress(i + 1, CONTENT_TYPES.length, 'Backing up content types')
      
      const documents = await backupContentType(client, contentType, includeDrafts)
      backupData[contentType] = documents
      totalDocuments += documents.length
    }
    
    // Save backup files
    console.log('\n💾 Saving backup files...')
    const savedFiles = await saveBackupFiles(backupData, backupPaths)
    
    // Generate summary
    const summary = {
      timestamp: new Date().toISOString(),
      backupType: includeDrafts ? 'full' : 'published',
      totalDocuments,
      contentTypes: Object.fromEntries(
        Object.entries(backupData).map(([type, docs]) => [type, docs.length])
      ),
      backupLocations: Object.keys(backupPaths),
      filesCreated: savedFiles.length
    }
    
    // Save backup summary
    Object.values(backupPaths).forEach(dirPath => {
      const summaryPath = path.join(dirPath, 'backup-summary.json')
      fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2))
    })
    
    logStats({
      totalDocuments,
      contentTypes: CONTENT_TYPES.length,
      backupLocations: Object.keys(backupPaths).length,
      filesCreated: savedFiles.length
    }, 'BACKUP SUMMARY')
    
    console.log('\n📄 Content breakdown:')
    Object.entries(backupData).forEach(([type, docs]) => {
      console.log(`  ${type}: ${docs.length} documents`)
    })
    
    logSuccess('Backup completed successfully!')
    logToolEnd('SANITY CMS BACKUP', true)
    
  } catch (error) {
    logError(`Backup failed: ${error.message}`)
    logToolEnd('SANITY CMS BACKUP', false)
    process.exit(1)
  }
}

// Run the backup
runBackup()
