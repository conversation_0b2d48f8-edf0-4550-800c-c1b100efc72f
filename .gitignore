# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
/node_modules
/.pnp
.pnp.js

# Compiled Sanity Studio
/dist

# Temporary Sanity runtime, generated by the CLI on every dev server start
/.sanity

# Logs
/logs
*.log

# Coverage directory used by testing tools
/coverage

# Misc
.DS_Store
*.pem

# Typescript
*.tsbuildinfo

# Dotenv and similar local-only files
*.local

node_modules/
.next/
.sanity/
dist/
.env
.env.local
.DS_Store
players list.csv

# Local Netlify folder
.netlify

# Backup files are tracked in git for safety
# Only ignore temporary backup files
backups/**/*.tmp
backups/**/*.log
GEMINI.md 
