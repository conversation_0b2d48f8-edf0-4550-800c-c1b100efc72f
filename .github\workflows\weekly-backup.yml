name: Weekly Full Backup

permissions:
  contents: write


on:
  # Run every Sunday at 3 AM UTC
  schedule:
    - cron: '0 3 * * 0'
  
  # Allow manual trigger
  workflow_dispatch:

jobs:
  weekly-backup:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        fetch-depth: 0
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm install
    
    - name: Configure Git
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
    
    - name: Create weekly backup directory
      run: |
        mkdir -p backups/weekly
        echo "backup_date=$(date +%Y-%m-%d)" >> $GITHUB_ENV
        echo "week_number=$(date +%Y-W%U)" >> $GITHUB_ENV
    
    - name: Run full backup
      env:
        SANITY_PROJECT_ID: ${{ secrets.SANITY_PROJECT_ID }}
        SANITY_DATASET: ${{ secrets.SANITY_DATASET }}
        SANITY_TOKEN: ${{ secrets.SANITY_TOKEN }}
      run: npm run backup-full
    
    - name: Copy to weekly backup
      run: |
        if [ -d "backups/daily/${{ env.backup_date }}" ]; then
          cp -r "backups/daily/${{ env.backup_date }}" "backups/weekly/${{ env.week_number }}"
          echo "Weekly backup created at backups/weekly/${{ env.week_number }}"
        fi
    
    - name: Clean old weekly backups (keep last 12 weeks)
      run: |
        if [ -d "backups/weekly" ]; then
          find backups/weekly -type d -name "20*" -mtime +84 -exec rm -rf {} + 2>/dev/null || true
        fi
    
    - name: Commit and push backup
      run: |
        git add backups/
        if git diff --staged --quiet; then
          echo "No changes to commit"
        else
          git commit -m "Weekly backup: ${{ env.week_number }} (${{ env.backup_date }})"
          git push
        fi
    
    - name: Create backup summary
      run: |
        echo "## 📦 Weekly Backup Summary" >> $GITHUB_STEP_SUMMARY
        echo "**Week:** ${{ env.week_number }}" >> $GITHUB_STEP_SUMMARY
        echo "**Date:** ${{ env.backup_date }}" >> $GITHUB_STEP_SUMMARY
        echo "**Type:** Full (including drafts)" >> $GITHUB_STEP_SUMMARY
        
        if [ -f "backups/weekly/${{ env.week_number }}/manifest.json" ]; then
          echo "**Details:**" >> $GITHUB_STEP_SUMMARY
          node -e "
            const manifest = require('./backups/weekly/${{ env.week_number }}/manifest.json');
            console.log('- Total documents: ' + manifest.summary.totalDocuments);
            console.log('- Successful types: ' + manifest.summary.successfulTypes);
            console.log('- Failed types: ' + manifest.summary.failedTypes);
            manifest.results.forEach(r => {
              if (r.status === 'success') {
                console.log('- ' + r.contentType + ': ' + r.count + ' documents');
              }
            });
          " >> $GITHUB_STEP_SUMMARY
        fi
