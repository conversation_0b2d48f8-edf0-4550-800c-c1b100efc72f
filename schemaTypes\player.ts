import {defineField, defineType} from 'sanity'

export default defineType({
  name: 'player',
  title: 'Player',
  type: 'document',
  fields: [
    defineField({
      name: 'nickname',
      title: 'Nickname',
      type: 'string',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'name',
      title: 'Real Name',
      type: 'string',
    }),
    defineField({
      name: 'image',
      title: 'Player Image',
      type: 'image',
      options: {
        hotspot: true,
      },
    }),
    defineField({
      name: 'team',
      title: 'Current Team',
      type: 'reference',
      to: [{type: 'team'}],
    }),
    defineField({
      name: 'country',
      title: 'Country',
      type: 'string',
    }),
    defineField({
      name: 'elo',
      title: 'ELO Rating',
      type: 'number',
      validation: (Rule) => Rule.required().min(0),
      initialValue: 1000,
    }),
    defineField({
      name: 'city',
      title: 'City',
      type: 'string',
    }),
    defineField({
      name: 'age',
      title: 'Age',
      type: 'number',
      validation: (Rule) => Rule.min(0),
    }),
    defineField({
      name: 'discord',
      title: 'Discord Tag',
      type: 'string',
    }),
  ],
  preview: {
    select: {
      title: 'nickname',
      subtitle: 'name',
      media: 'image',
    },
  },
}) 