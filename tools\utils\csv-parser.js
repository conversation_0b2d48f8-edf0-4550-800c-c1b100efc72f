/**
 * CSV Parser Utilities
 * 
 * Provides standardized CSV parsing functionality for player data
 * with consistent column handling and data validation.
 */

const fs = require('fs')
const { parse } = require('csv-parse')

// Configuration constants
const CSV_FILE_PATH = 'players list.csv'
const NICKNAME_COLUMN_INDEX = 2 // Column C (0-indexed)
const NAME_COLUMN_INDEX = 3     // Column D (0-indexed)
const ELO_COLUMN_INDEX = 4      // Column E (0-indexed)
const HEADER_ROWS_TO_SKIP = 6   // Skip first 6 header rows

/**
 * Reads and parses the player CSV file
 * @param {string} filePath - Path to the CSV file (optional, defaults to standard path)
 * @returns {Promise<Array>} Array of player objects
 */
async function parsePlayerCSV(filePath = CSV_FILE_PATH) {
  console.log(`📄 Reading CSV file: ${filePath}`)
  
  if (!fs.existsSync(filePath)) {
    throw new Error(`CSV file not found: ${filePath}`)
  }

  const fileContent = fs.readFileSync(filePath, 'utf-8')
  
  return new Promise((resolve, reject) => {
    const players = []
    
    parse(fileContent, {
      columns: false, // Don't use first row as headers
      skip_empty_lines: true,
      trim: true,
      from_line: HEADER_ROWS_TO_SKIP + 1, // Skip header rows (1-indexed)
    }, (err, records) => {
      if (err) {
        console.error('❌ Error parsing CSV:', err)
        reject(err)
        return
      }

      records.forEach((record, index) => {
        try {
          const nickname = record[NICKNAME_COLUMN_INDEX]?.trim()
          const name = record[NAME_COLUMN_INDEX]?.trim()
          const eloStr = record[ELO_COLUMN_INDEX]?.trim()

          // Skip rows without nickname
          if (!nickname) {
            return
          }

          // Parse ELO rating
          const elo = parseFloat(eloStr) || 0

          // Filter out players with ELO 0 (inactive players)
          if (elo <= 0) {
            return
          }

          players.push({
            nickname,
            name: name || '',
            elo
          })
        } catch (error) {
          console.warn(`⚠️  Skipping row ${index + HEADER_ROWS_TO_SKIP + 1}: ${error.message}`)
        }
      })

      console.log(`✅ Parsed ${players.length} active players from CSV`)
      resolve(players)
    })
  })
}

/**
 * Validates player data structure
 * @param {Object} player - Player object to validate
 * @returns {boolean} True if player data is valid
 */
function validatePlayerData(player) {
  if (!player || typeof player !== 'object') {
    return false
  }
  
  if (!player.nickname || typeof player.nickname !== 'string') {
    return false
  }
  
  if (typeof player.elo !== 'number' || player.elo < 0) {
    return false
  }
  
  return true
}

/**
 * Filters and validates an array of players
 * @param {Array} players - Array of player objects
 * @returns {Array} Array of valid players
 */
function filterValidPlayers(players) {
  return players.filter(player => {
    const isValid = validatePlayerData(player)
    if (!isValid) {
      console.warn(`⚠️  Invalid player data:`, player)
    }
    return isValid
  })
}

module.exports = {
  parsePlayerCSV,
  validatePlayerData,
  filterValidPlayers,
  CSV_FILE_PATH,
  NICKNAME_COLUMN_INDEX,
  NAME_COLUMN_INDEX,
  ELO_COLUMN_INDEX
}
