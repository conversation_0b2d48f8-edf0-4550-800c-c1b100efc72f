/**
 * Backup Status Tool for CARX HLTV
 * 
 * Displays status and information about existing backups.
 * 
 * Usage: npm run backup-status
 */

const { logToolStart, logToolEnd, logSubsection, logStats } = require('../utils/logger')
const fs = require('fs')
const path = require('path')

function formatFileSize(bytes) {
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  if (bytes === 0) return '0 Bytes'
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

function formatDate(dateString) {
  return new Date(dateString).toLocaleString()
}

function getDirectorySize(dirPath) {
  if (!fs.existsSync(dirPath)) return 0
  
  let totalSize = 0
  const files = fs.readdirSync(dirPath, { withFileTypes: true })
  
  for (const file of files) {
    const filePath = path.join(dirPath, file.name)
    if (file.isDirectory()) {
      totalSize += getDirectorySize(filePath)
    } else {
      totalSize += fs.statSync(filePath).size
    }
  }
  
  return totalSize
}

function analyzeBackupDirectory(backupPath, type) {
  if (!fs.existsSync(backupPath)) {
    return { type, exists: false, backups: [] }
  }
  
  const backups = []
  const entries = fs.readdirSync(backupPath, { withFileTypes: true })
  
  for (const entry of entries) {
    if (entry.isDirectory()) {
      const backupDir = path.join(backupPath, entry.name)
      const summaryPath = path.join(backupDir, 'backup-summary.json')
      
      let summary = null
      if (fs.existsSync(summaryPath)) {
        try {
          summary = JSON.parse(fs.readFileSync(summaryPath, 'utf-8'))
        } catch (error) {
          console.warn(`⚠️  Could not read summary for ${entry.name}`)
        }
      }
      
      const size = getDirectorySize(backupDir)
      const stats = fs.statSync(backupDir)
      
      backups.push({
        name: entry.name,
        path: backupDir,
        size,
        created: stats.birthtime,
        modified: stats.mtime,
        summary
      })
    }
  }
  
  // Sort by creation date (newest first)
  backups.sort((a, b) => b.created - a.created)
  
  return {
    type,
    exists: true,
    backups,
    totalSize: backups.reduce((sum, backup) => sum + backup.size, 0),
    count: backups.length
  }
}

function displayBackupStatus() {
  logToolStart('BACKUP STATUS', 'Display Backup Information')
  
  const backupTypes = [
    { type: 'Daily', path: 'backups/daily' },
    { type: 'Weekly', path: 'backups/weekly' },
    { type: 'Timestamped', path: 'backups/timestamped' }
  ]
  
  const allBackups = []
  let totalSize = 0
  let totalCount = 0
  
  for (const { type, path: backupPath } of backupTypes) {
    const analysis = analyzeBackupDirectory(backupPath, type)
    allBackups.push(analysis)
    
    if (analysis.exists) {
      totalSize += analysis.totalSize
      totalCount += analysis.count
      
      logSubsection(`${type} Backups (${analysis.count} backups, ${formatFileSize(analysis.totalSize)})`)
      
      if (analysis.backups.length === 0) {
        console.log('  No backups found')
      } else {
        // Show latest 5 backups
        const recentBackups = analysis.backups.slice(0, 5)
        recentBackups.forEach(backup => {
          const typeInfo = backup.summary ? 
            ` (${backup.summary.backupType}, ${backup.summary.totalDocuments} docs)` : ''
          console.log(`  📁 ${backup.name} - ${formatFileSize(backup.size)} - ${formatDate(backup.created)}${typeInfo}`)
        })
        
        if (analysis.backups.length > 5) {
          console.log(`  ... and ${analysis.backups.length - 5} more`)
        }
      }
    } else {
      logSubsection(`${type} Backups`)
      console.log('  Directory does not exist')
    }
  }
  
  // Overall statistics
  logStats({
    totalBackups: totalCount,
    totalSize: formatFileSize(totalSize),
    dailyBackups: allBackups[0].exists ? allBackups[0].count : 0,
    weeklyBackups: allBackups[1].exists ? allBackups[1].count : 0,
    timestampedBackups: allBackups[2].exists ? allBackups[2].count : 0
  }, 'BACKUP OVERVIEW')
  
  // Latest backup info
  const latestBackups = allBackups
    .filter(analysis => analysis.exists && analysis.backups.length > 0)
    .map(analysis => analysis.backups[0])
    .sort((a, b) => b.created - a.created)
  
  if (latestBackups.length > 0) {
    const latest = latestBackups[0]
    logSubsection('📅 LATEST BACKUP')
    console.log(`  Name: ${latest.name}`)
    console.log(`  Created: ${formatDate(latest.created)}`)
    console.log(`  Size: ${formatFileSize(latest.size)}`)
    if (latest.summary) {
      console.log(`  Type: ${latest.summary.backupType}`)
      console.log(`  Documents: ${latest.summary.totalDocuments}`)
      console.log(`  Content Types: ${Object.keys(latest.summary.contentTypes).join(', ')}`)
    }
  } else {
    logSubsection('📅 LATEST BACKUP')
    console.log('  No backups found')
  }
  
  // Recommendations
  logSubsection('💡 RECOMMENDATIONS')
  
  if (totalCount === 0) {
    console.log('  ⚠️  No backups found! Run "npm run backup" to create your first backup.')
  } else {
    const latestAge = latestBackups.length > 0 ? 
      (Date.now() - latestBackups[0].created.getTime()) / (1000 * 60 * 60 * 24) : Infinity
    
    if (latestAge > 7) {
      console.log('  ⚠️  Latest backup is over a week old. Consider running a fresh backup.')
    } else if (latestAge > 1) {
      console.log('  ✅ Backups are reasonably recent.')
    } else {
      console.log('  ✅ Backups are up to date.')
    }
    
    if (totalSize > 100 * 1024 * 1024) { // 100MB
      console.log('  💾 Backup storage is getting large. Consider cleaning up old backups.')
    }
  }
  
  logToolEnd('BACKUP STATUS', true)
}

// Run the status check
displayBackupStatus()
