import '../styles/globals.css'
import type { AppProps } from 'next/app'
import { LanguageProvider } from '../lib/LanguageContext'
import Layout from '../components/Layout'
import { useRouter } from 'next/router'

export default function App({ Component, pageProps }: AppProps) {
  const router = useRouter()

  // Pages that should not use the standard layout
  const pagesWithoutLayout = ['/not_allowed']
  const shouldUseLayout = !pagesWithoutLayout.includes(router.pathname)

  // Determine current page for header highlighting
  const getCurrentPage = () => {
    const path = router.pathname
    if (path === '/news') return 'news'
    if (path === '/tournaments') return 'tournaments'
    if (path === '/players') return 'players'
    if (path === '/teams') return 'teams'
    if (path === '/partners') return 'partners'
    if (path === '/contact') return 'contact'
    return undefined
  }

  return (
    <LanguageProvider>
      {shouldUseLayout ? (
        <Layout currentPage={getCurrentPage()}>
          <Component {...pageProps} />
        </Layout>
      ) : (
        <Component {...pageProps} />
      )}
    </LanguageProvider>
  )
}