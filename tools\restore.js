const { createClient } = require('@sanity/client')
const fs = require('fs')
const path = require('path')
const readline = require('readline')
require('dotenv').config()

// Sanity client configuration
const client = createClient({
  projectId: process.env.SANITY_PROJECT_ID,
  dataset: process.env.SANITY_DATASET,
  apiVersion: '2023-05-24',
  token: process.env.SANITY_TOKEN,
  useCdn: false
})

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

// Prompt user for confirmation
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer)
    })
  })
}

// List available backups
function listAvailableBackups() {
  const backupDir = path.join(process.cwd(), 'backups', 'daily')
  
  if (!fs.existsSync(backupDir)) {
    console.log('❌ No backups found. Run "npm run backup" first.')
    return []
  }
  
  const backupDates = fs.readdirSync(backupDir)
    .filter(dir => fs.statSync(path.join(backupDir, dir)).isDirectory())
    .sort()
    .reverse() // Most recent first
  
  return backupDates.map(date => ({
    date,
    path: path.join(backupDir, date)
  }))
}

// Validate backup directory
function validateBackup(backupPath) {
  const manifestPath = path.join(backupPath, 'manifest.json')
  
  if (!fs.existsSync(manifestPath)) {
    return { valid: false, error: 'No manifest.json found' }
  }
  
  try {
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'))
    return { valid: true, manifest }
  } catch (error) {
    return { valid: false, error: 'Invalid manifest.json' }
  }
}

// Restore content type
async function restoreContentType(contentType, backupPath, options = {}) {
  const filePath = path.join(backupPath, `${contentType}.json`)
  
  if (!fs.existsSync(filePath)) {
    console.log(`   ⚠️  No backup file found for ${contentType}`)
    return { contentType, status: 'skipped', reason: 'no backup file' }
  }
  
  try {
    const documents = JSON.parse(fs.readFileSync(filePath, 'utf8'))
    
    if (documents.length === 0) {
      console.log(`   ⚠️  No documents to restore for ${contentType}`)
      return { contentType, status: 'skipped', reason: 'no documents' }
    }
    
    console.log(`📦 Restoring ${documents.length} ${contentType} documents...`)
    
    let created = 0
    let updated = 0
    let errors = 0
    
    for (const doc of documents) {
      try {
        // Check if document exists
        const existing = await client.fetch(`*[_id == $id][0]`, { id: doc._id })
        
        if (existing) {
          if (options.overwrite) {
            // Update existing document
            await client.createOrReplace(doc)
            updated++
            console.log(`   ✏️  Updated: ${doc._id}`)
          } else {
            console.log(`   ⏭️  Skipped existing: ${doc._id}`)
          }
        } else {
          // Create new document
          await client.create(doc)
          created++
          console.log(`   ✅ Created: ${doc._id}`)
        }
      } catch (error) {
        errors++
        console.error(`   ❌ Error with ${doc._id}:`, error.message)
      }
    }
    
    console.log(`   📊 ${contentType}: ${created} created, ${updated} updated, ${errors} errors`)
    return { 
      contentType, 
      status: 'completed', 
      created, 
      updated, 
      errors,
      total: documents.length 
    }
    
  } catch (error) {
    console.error(`   ❌ Failed to restore ${contentType}:`, error.message)
    return { contentType, status: 'error', error: error.message }
  }
}

// Main restore function
async function runRestore() {
  console.log('🔄 Sanity CMS Restore Tool')
  console.log('─'.repeat(50))
  
  // List available backups
  const backups = listAvailableBackups()
  
  if (backups.length === 0) {
    console.log('❌ No backups available.')
    rl.close()
    return
  }
  
  console.log('📅 Available backups:')
  backups.forEach((backup, index) => {
    const validation = validateBackup(backup.path)
    const status = validation.valid ? '✅' : '❌'
    console.log(`   ${index + 1}. ${backup.date} ${status}`)
    if (validation.valid && validation.manifest) {
      console.log(`      📦 ${validation.manifest.summary.totalDocuments} documents`)
    }
  })
  
  console.log('─'.repeat(50))
  
  // Get user selection
  const selection = await askQuestion('Select backup number (or "q" to quit): ')
  
  if (selection.toLowerCase() === 'q') {
    console.log('👋 Restore cancelled.')
    rl.close()
    return
  }
  
  const backupIndex = parseInt(selection) - 1
  if (isNaN(backupIndex) || backupIndex < 0 || backupIndex >= backups.length) {
    console.log('❌ Invalid selection.')
    rl.close()
    return
  }
  
  const selectedBackup = backups[backupIndex]
  const validation = validateBackup(selectedBackup.path)
  
  if (!validation.valid) {
    console.log(`❌ Invalid backup: ${validation.error}`)
    rl.close()
    return
  }
  
  console.log(`\n📋 Backup Details:`)
  console.log(`   📅 Date: ${validation.manifest.date}`)
  console.log(`   🏗️  Project: ${validation.manifest.projectId}`)
  console.log(`   📊 Dataset: ${validation.manifest.dataset}`)
  console.log(`   📦 Documents: ${validation.manifest.summary.totalDocuments}`)
  console.log(`   🔧 Type: ${validation.manifest.backupType}`)
  
  // Confirm restore
  const confirm = await askQuestion('\n⚠️  This will modify your CMS data. Continue? (yes/no): ')
  
  if (confirm.toLowerCase() !== 'yes') {
    console.log('👋 Restore cancelled.')
    rl.close()
    return
  }
  
  // Ask about overwriting existing documents
  const overwrite = await askQuestion('Overwrite existing documents? (yes/no): ')
  const options = {
    overwrite: overwrite.toLowerCase() === 'yes'
  }
  
  console.log('\n🚀 Starting restore...')
  console.log('─'.repeat(50))
  
  // Test connection
  try {
    await client.fetch('*[_type == "player"][0]')
    console.log('✅ Sanity connection verified')
  } catch (error) {
    console.error('❌ Failed to connect to Sanity:', error.message)
    rl.close()
    return
  }
  
  // Restore each content type
  const results = []
  const contentTypes = validation.manifest.results
    .filter(r => r.status === 'success' && r.contentType !== 'assets')
    .map(r => r.contentType)
  
  for (const contentType of contentTypes) {
    const result = await restoreContentType(contentType, selectedBackup.path, options)
    results.push(result)
  }
  
  console.log('─'.repeat(50))
  console.log('📋 Restore Summary:')
  
  let totalCreated = 0
  let totalUpdated = 0
  let totalErrors = 0
  
  results.forEach(result => {
    if (result.status === 'completed') {
      totalCreated += result.created || 0
      totalUpdated += result.updated || 0
      totalErrors += result.errors || 0
    }
  })
  
  console.log(`   ✅ Created: ${totalCreated}`)
  console.log(`   ✏️  Updated: ${totalUpdated}`)
  console.log(`   ❌ Errors: ${totalErrors}`)
  
  if (totalErrors > 0) {
    console.log('\n❌ Some documents failed to restore. Check the logs above.')
  } else {
    console.log('\n🎉 Restore completed successfully!')
  }
  
  rl.close()
}

// Run restore if called directly
if (require.main === module) {
  runRestore().catch(error => {
    console.error('💥 Restore failed:', error)
    rl.close()
    process.exit(1)
  })
}

module.exports = { runRestore }
