// Simple test script to verify the contact API works
// Run with: node test-contact-api.js
// Make sure the Next.js dev server is running on localhost:3000

const testContactAPI = async () => {
  const testData = {
    name: "Test User",
    // The API expects a Discord tag, not an email address
    discord: "@testuser",
    subject: "Test Subject",
    message: "This is a test message to verify the contact form API is working correctly."
  };

  try {
    console.log('🧪 Testing contact API...');
    console.log('📝 Test data:', testData);
    console.log('🌐 Sending request to http://localhost:3000/api/contact');

    const response = await fetch('http://localhost:3000/api/contact', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });

    const result = await response.json();

    console.log('📊 Response status:', response.status);
    console.log('📋 Response data:', JSON.stringify(result, null, 2));

    if (response.ok) {
      console.log('✅ Contact API test passed!');
      console.log('🆔 Support request ID:', result.requestId);
    } else {
      console.log('❌ Contact API test failed!');
      console.log('💥 Error:', result.message);

      if (result.error && result.error.message) {
        console.log('🔍 Detailed error:', result.error.message);

        if (result.error.message.includes('Insufficient permissions')) {
          console.log('\n🔧 SOLUTION: The Sanity token needs create permissions.');
          console.log('   1. Go to https://sanity.io/manage');
          console.log('   2. Select your project');
          console.log('   3. Go to API → Tokens');
          console.log('   4. Create a new token with "Editor" or "Admin" permissions');
          console.log('   5. Update your SANITY_TOKEN environment variable');
          console.log('   6. Restart the development server');
        }
      }
    }
  } catch (error) {
    console.error('❌ Test failed with network error:', error.message);
    console.log('\n🔍 Possible causes:');
    console.log('   - Next.js dev server is not running (run: npm run dev)');
    console.log('   - Server is running on a different port');
    console.log('   - Network connectivity issues');
  }
};

// Only run if this file is executed directly
if (require.main === module) {
  testContactAPI();
}

module.exports = { testContactAPI };
