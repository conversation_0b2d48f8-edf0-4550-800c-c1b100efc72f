/**
 * Logging and Reporting Utilities
 * 
 * Provides standardized logging, progress reporting, and file output
 * functionality for all maintenance tools.
 */

const fs = require('fs')
const path = require('path')

/**
 * Creates a timestamped filename
 * @param {string} prefix - Filename prefix
 * @param {string} extension - File extension (default: 'json')
 * @returns {string} Timestamped filename
 */
function createTimestampedFilename(prefix, extension = 'json') {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  return `${prefix}-${timestamp}.${extension}`
}

/**
 * Saves a report to a JSON file in the tools directory
 * @param {Object} data - Data to save
 * @param {string} filename - Filename (will be timestamped if no timestamp present)
 * @returns {string} Path to saved file
 */
function saveReport(data, filename) {
  const reportPath = path.join('tools', filename)
  
  const report = {
    timestamp: new Date().toISOString(),
    ...data
  }
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
  console.log(`📄 Report saved: ${reportPath}`)
  
  return reportPath
}

/**
 * Logs a section header with decorative formatting
 * @param {string} title - Section title
 * @param {string} char - Character to use for decoration (default: '=')
 * @param {number} width - Width of the decoration (default: 80)
 */
function logSection(title, char = '=', width = 80) {
  console.log('\n' + char.repeat(width))
  console.log(title.toUpperCase())
  console.log(char.repeat(width))
}

/**
 * Logs a subsection header
 * @param {string} title - Subsection title
 * @param {string} char - Character to use for decoration (default: '-')
 * @param {number} width - Width of the decoration (default: 60)
 */
function logSubsection(title, char = '-', width = 60) {
  console.log('\n' + title)
  console.log(char.repeat(width))
}

/**
 * Logs progress for batch operations
 * @param {number} current - Current item number
 * @param {number} total - Total number of items
 * @param {string} operation - Description of the operation
 */
function logProgress(current, total, operation = 'Processing') {
  const percentage = Math.round((current / total) * 100)
  console.log(`📊 ${operation}: ${current}/${total} (${percentage}%)`)
}

/**
 * Logs a success message with checkmark
 * @param {string} message - Success message
 */
function logSuccess(message) {
  console.log(`✅ ${message}`)
}

/**
 * Logs an error message with X mark
 * @param {string} message - Error message
 */
function logError(message) {
  console.log(`❌ ${message}`)
}

/**
 * Logs a warning message with warning symbol
 * @param {string} message - Warning message
 */
function logWarning(message) {
  console.log(`⚠️  ${message}`)
}

/**
 * Logs an info message with info symbol
 * @param {string} message - Info message
 */
function logInfo(message) {
  console.log(`ℹ️  ${message}`)
}

/**
 * Logs tool startup information
 * @param {string} toolName - Name of the tool
 * @param {string} description - Tool description
 */
function logToolStart(toolName, description) {
  logSection(`${toolName} - ${description}`)
  console.log(`📅 Started: ${new Date().toISOString()}`)
  console.log(`🏗️  Project: ${process.env.NEXT_PUBLIC_SANITY_PROJECT_ID}`)
  console.log(`📊 Dataset: ${process.env.NEXT_PUBLIC_SANITY_DATASET}`)
}

/**
 * Logs tool completion
 * @param {string} toolName - Name of the tool
 * @param {boolean} success - Whether the tool completed successfully
 */
function logToolEnd(toolName, success = true) {
  const status = success ? '✅ COMPLETED' : '❌ FAILED'
  logSection(`${toolName} ${status}`)
  console.log(`📅 Finished: ${new Date().toISOString()}`)
}

/**
 * Logs statistics in a formatted table
 * @param {Object} stats - Statistics object with key-value pairs
 * @param {string} title - Title for the statistics section
 */
function logStats(stats, title = 'STATISTICS') {
  logSubsection(`📊 ${title}`)
  
  Object.entries(stats).forEach(([key, value]) => {
    const formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
    console.log(`  ${formattedKey}: ${value}`)
  })
}

module.exports = {
  createTimestampedFilename,
  saveReport,
  logSection,
  logSubsection,
  logProgress,
  logSuccess,
  logError,
  logWarning,
  logInfo,
  logToolStart,
  logToolEnd,
  logStats
}
