import type { NextApiRequest, NextApiResponse } from 'next'
import fs from 'fs'
import path from 'path'

interface ContactFormData {
  name: string
  discord: string
  subject: string
  message: string
}

// Helper function to convert string to rich text format
function stringToPortableText(text: string) {
  return [
    {
      _type: 'block',
      _key: Math.random().toString(36).substr(2, 9),
      style: 'normal',
      markDefs: [],
      children: [
        {
          _type: 'span',
          _key: Math.random().toString(36).substr(2, 9),
          text: text,
          marks: []
        }
      ]
    }
  ]
}

interface ValidationError {
  field: string
  message: string
}

function validateContactForm(data: any): { isValid: boolean; errors: ValidationError[] } {
  const errors: ValidationError[] = []

  if (!data.name || typeof data.name !== 'string' || !data.name.trim()) {
    errors.push({ field: 'name', message: 'Name is required' })
  }

  if (!data.discord || typeof data.discord !== 'string' || !data.discord.trim()) {
    errors.push({ field: 'discord', message: 'Discord tag is required' })
  } else if (!/^@[a-z0-9._]{2,32}$/.test(data.discord)) {
    errors.push({ field: 'discord', message: 'Please enter a valid Discord tag (e.g., @username)' })
  }

  if (!data.subject || typeof data.subject !== 'string' || !data.subject.trim()) {
    errors.push({ field: 'subject', message: 'Subject is required' })
  }

  if (!data.message || typeof data.message !== 'string' || !data.message.trim()) {
    errors.push({ field: 'message', message: 'Message is required' })
  } else if (data.message.trim().length < 10) {
    errors.push({ field: 'message', message: 'Message must be at least 10 characters long' })
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

function logContactSubmission(formData: ContactFormData): string {
  const timestamp = new Date().toISOString()
  const requestId = `contact_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

  const logEntry = {
    id: requestId,
    timestamp,
    name: formData.name,
    discord: formData.discord,
    subject: formData.subject,
    message: formData.message,
    status: 'new',
    priority: 'medium'
  }

  try {
    // Create logs directory if it doesn't exist
    const logsDir = path.join(process.cwd(), 'logs')
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true })
    }

    // Append to contact submissions log file
    const logFile = path.join(logsDir, 'contact-submissions.jsonl')
    const logLine = JSON.stringify(logEntry) + '\n'

    fs.appendFileSync(logFile, logLine, 'utf8')

    console.log(`CONTACT FALLBACK: Logged submission to file: ${requestId}`)
    return requestId
  } catch (error) {
    console.error('CONTACT FALLBACK: Failed to log submission:', error)
    throw new Error('Failed to save contact submission')
  }
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const timestamp = new Date().toISOString()
  console.log(`CONTACT FALLBACK API: ${req.method} request received at ${timestamp}`)

  // Only allow POST requests
  if (req.method !== 'POST') {
    console.warn(`CONTACT FALLBACK API: Method ${req.method} not allowed`)
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    })
  }

  try {
    // Validate request body
    const validation = validateContactForm(req.body)

    if (!validation.isValid) {
      console.warn('CONTACT FALLBACK API: Validation failed:', validation.errors)
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validation.errors
      })
    }

    const formData: ContactFormData = {
      name: req.body.name.trim(),
      discord: req.body.discord.trim(),
      subject: req.body.subject.trim(),
      message: req.body.message.trim()
    }

    console.log(`CONTACT FALLBACK API: Processing submission from ${formData.discord}`)

    // Log the submission to a file instead of Sanity
    const requestId = logContactSubmission(formData)

    console.log(`CONTACT FALLBACK API: Contact submission logged with ID: ${requestId}`)

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Contact submission received and logged successfully',
      requestId: requestId,
      note: 'This submission was logged to file. Please check server logs for admin access.'
    })

  } catch (error) {
    console.error('CONTACT FALLBACK API: Error processing request:', error)

    return res.status(500).json({
      success: false,
      message: 'Failed to process contact submission. Please try again later.',
      error: process.env.NODE_ENV === 'development' ? {
        message: error instanceof Error ? error.message : 'Unknown error'
      } : undefined
    })
  }
}
