# Maintenance Tools

This directory contains streamlined utility scripts for maintaining the CARX HLTV application. These tools are organized into categories and use shared utilities to reduce code duplication.

## Tool Categories

### Player Management Tools

#### Player Import (`npm run import-players`)
- **File**: `tools/player/import.js`
- **Purpose**: Import/update player data from CSV file into Sanity CMS
- **Features**: Only updates ELO and nickname fields, preserves existing data, batch processing

#### Player Comparison (`npm run compare-players`)
- **File**: `tools/player/compare.js`
- **Purpose**: Compare players between Sanity CMS and CSV file
- **Features**: Identifies discrepancies, generates detailed reports, filters active players only

#### Player Cleanup (`npm run cleanup-players`)
- **File**: `tools/player/cleanup.js`
- **Purpose**: Remove obsolete players without photos
- **Features**: Preserves players with photos for manual review, confirmation prompts

### Backup & Restore Tools

#### Backup (`npm run backup` / `npm run backup-full`)
- **File**: `tools/backup/backup.js`
- **Purpose**: Create automated backups of all Sanity CMS content
- **Features**: Daily/weekly/timestamped storage, published-only or full backups

#### Backup Status (`npm run backup-status`)
- **File**: `tools/backup/status.js`
- **Purpose**: Display backup information and recommendations
- **Features**: Size analysis, latest backup info, storage recommendations

#### Restore (`npm run restore`)
- **File**: `tools/restore.js`
- **Purpose**: Restore content from backup files
- **Features**: Interactive restoration, safety confirmations

## Shared Utilities

The tools use shared utilities located in `tools/utils/`:

- **`sanity-client.js`**: Standardized Sanity client setup and connection testing
- **`csv-parser.js`**: Reusable CSV parsing for player data
- **`logger.js`**: Consistent logging, progress reporting, and file output

## Prerequisites

1. **Environment Variables**: Ensure your `.env` file contains:
   ```
   NEXT_PUBLIC_SANITY_PROJECT_ID=your_project_id
   NEXT_PUBLIC_SANITY_DATASET=your_dataset
   SANITY_TOKEN=your_token
   ```

2. **CSV File**: For player tools, ensure `players list.csv` exists in the root directory

## CSV File Format

Player tools expect a CSV file named `players list.csv` in the root directory with this structure:

- **Column C (index 2)**: Nickname (main identifier)
- **Column D (index 3)**: Real name
- **Column E (index 4)**: ELO rating
- **Rows 1-6**: Headers and metadata (automatically skipped)
- **Row 7+**: Player data

## Recommended Workflow

1. **Compare first**: `npm run compare-players` - Identify differences between Sanity and CSV
2. **Import updates**: `npm run import-players` - Update player data from CSV
3. **Cleanup obsolete**: `npm run cleanup-players` - Remove players no longer active
4. **Backup regularly**: `npm run backup` - Create backups before major changes
5. **Monitor status**: `npm run backup-status` - Check backup health

## Generated Reports

All tools generate timestamped JSON reports in the `tools/` directory for detailed analysis and record-keeping.

## Benefits of Streamlined Structure

- **Reduced Code Duplication**: Shared utilities eliminate repeated code
- **Consistent Interface**: Standardized logging and error handling across all tools
- **Better Organization**: Related tools grouped together logically
- **Easier Maintenance**: Changes to common functionality only need to be made once
- **Improved Reliability**: Centralized client setup and validation