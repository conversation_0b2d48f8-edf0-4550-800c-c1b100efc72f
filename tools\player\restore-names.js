/**
 * Player Name Restoration Tool for CARX HLTV
 * 
 * Restores player names from backup without affecting other data.
 * Used to fix accidental name overwrites during import.
 * 
 * Usage: node tools/player/restore-names.js
 */

const { createAndTestClient } = require('../utils/sanity-client')
const { logToolStart, logToolEnd, logProgress, logSuccess, logError, logStats } = require('../utils/logger')
const fs = require('fs')
const path = require('path')

async function restorePlayerNames() {
  try {
    logToolStart('PLAYER NAME RESTORATION', 'Restore Names from Backup')
    
    // Initialize Sanity client
    const client = await createAndTestClient()
    
    // Find today's backup
    const backupPath = path.join(process.cwd(), 'backups', 'daily', '2025-06-26', 'player.json')
    
    if (!fs.existsSync(backupPath)) {
      logError('No backup found for today. Cannot restore names.')
      return
    }
    
    console.log('📄 Reading backup file...')
    const backupData = JSON.parse(fs.readFileSync(backupPath, 'utf8'))
    
    // Filter backup data to only players with names
    const playersWithNames = backupData.filter(player => player.name && player.name.trim() !== '')
    
    console.log(`✅ Found ${playersWithNames.length} players with names in backup`)
    
    // Get current players from Sanity
    console.log('🔍 Fetching current players from Sanity...')
    const currentPlayers = await client.fetch(`
      *[_type == "player" && !(_id in path("drafts.**"))] {
        _id,
        nickname,
        name
      }
    `)
    
    // Find players whose names need to be restored
    const playersToRestore = []
    
    for (const backupPlayer of playersWithNames) {
      const currentPlayer = currentPlayers.find(p => p._id === backupPlayer._id)
      
      if (currentPlayer && (!currentPlayer.name || currentPlayer.name.trim() === '')) {
        playersToRestore.push({
          id: currentPlayer._id,
          nickname: currentPlayer.nickname,
          nameToRestore: backupPlayer.name
        })
      }
    }
    
    logStats({
      totalBackupPlayers: backupData.length,
      playersWithNamesInBackup: playersWithNames.length,
      currentPlayers: currentPlayers.length,
      playersToRestore: playersToRestore.length
    })
    
    if (playersToRestore.length === 0) {
      console.log('\n✅ No players need name restoration!')
      logToolEnd('PLAYER NAME RESTORATION', true)
      return
    }
    
    console.log('\n🔄 PLAYERS TO RESTORE:')
    playersToRestore.forEach(player => {
      console.log(`  • ${player.nickname} → "${player.nameToRestore}"`)
    })
    
    console.log(`\n⚠️  About to restore names for ${playersToRestore.length} players. Continue? (y/N)`)
    
    // For automated runs, you might want to add a --force flag
    const readline = require('readline')
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    })
    
    const answer = await new Promise(resolve => {
      rl.question('', resolve)
    })
    rl.close()
    
    if (answer.toLowerCase() !== 'y') {
      console.log('👋 Name restoration cancelled.')
      logToolEnd('PLAYER NAME RESTORATION', false)
      return
    }
    
    // Restore names in batches
    console.log('\n🔄 Restoring player names...')
    const batchSize = 10
    let restoreCount = 0
    
    for (let i = 0; i < playersToRestore.length; i += batchSize) {
      const batch = playersToRestore.slice(i, i + batchSize)
      
      const transaction = client.transaction()
      
      batch.forEach(player => {
        transaction.patch(player.id, { set: { name: player.nameToRestore } })
      })
      
      try {
        await transaction.commit()
        restoreCount += batch.length
        logProgress(restoreCount, playersToRestore.length, 'Restoring names')
      } catch (error) {
        logError(`Failed to restore batch starting at index ${i}: ${error.message}`)
      }
    }
    
    logStats({
      playersRestored: restoreCount,
      totalRequested: playersToRestore.length
    }, 'RESTORATION RESULTS')
    
    if (restoreCount === playersToRestore.length) {
      logSuccess(`Successfully restored names for ${restoreCount} players`)
      logToolEnd('PLAYER NAME RESTORATION', true)
    } else {
      logError(`Only restored ${restoreCount} out of ${playersToRestore.length} players`)
      logToolEnd('PLAYER NAME RESTORATION', false)
    }
    
  } catch (error) {
    logError(`Name restoration failed: ${error.message}`)
    logToolEnd('PLAYER NAME RESTORATION', false)
    throw error
  }
}

// Run if called directly
if (require.main === module) {
  restorePlayerNames().catch(error => {
    console.error('💥 Name restoration failed:', error)
    process.exit(1)
  })
}

module.exports = { restorePlayerNames }
