import { useState } from 'react'
import { useLanguage } from '../lib/LanguageContext'

interface FormData {
  name: string
  discord: string
  subject: string
  message: string
}

interface FormErrors {
  name?: string
  discord?: string
  subject?: string
  message?: string
}

export default function ContactForm() {
  const { t } = useLanguage()
  const [formData, setFormData] = useState<FormData>({
    name: '',
    discord: '',
    subject: '',
    message: ''
  })
  const [errors, setErrors] = useState<FormErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.name.trim()) {
      newErrors.name = t('contact_form_name_required')
    }

    if (!formData.discord.trim()) {
      newErrors.discord = t('contact_form_discord_required')
    } else if (!/^@[a-z0-9._]{2,32}$/.test(formData.discord)) {
      newErrors.discord = t('contact_form_discord_invalid')
    }

    if (!formData.subject.trim()) {
      newErrors.subject = t('contact_form_subject_required')
    }

    if (!formData.message.trim()) {
      newErrors.message = t('contact_form_message_required')
    } else if (formData.message.trim().length < 10) {
      newErrors.message = t('contact_form_message_too_short')
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    setSubmitStatus('idle')

    try {
      // Try the main Sanity API first
      let response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      // If main API fails with permissions error, try fallback
      if (!response.ok) {
        const errorData = await response.json()

        if (errorData.message && errorData.message.includes('Configuration error')) {
          console.log('Main API failed, trying fallback...')

          response = await fetch('/api/contact-fallback', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData),
          })
        }
      }

      if (response.ok) {
        setSubmitStatus('success')
        setFormData({ name: '', discord: '', subject: '', message: '' })
        setErrors({})
      } else {
        setSubmitStatus('error')
      }
    } catch (error) {
      console.error('Error submitting form:', error)
      setSubmitStatus('error')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))

    // Clear error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [name]: undefined }))
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {submitStatus === 'success' && (
        <div className="p-4 rounded-lg bg-green-900/50 border border-green-700 text-green-100">
          <p className="font-medium">{t('contact_form_success_title')}</p>
          <p className="text-sm text-green-200 mt-1">
            {t('contact_form_success_message')}
          </p>
        </div>
      )}

      {submitStatus === 'error' && (
        <div className="p-4 rounded-lg bg-red-900/50 border border-red-700 text-red-100">
          <p className="font-medium">{t('contact_form_error_title')}</p>
          <p className="text-sm text-red-200 mt-1">
            {t('contact_form_error_message')}
          </p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-zinc-300 mb-2">
            {t('contact_form_name_label')} *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className={`w-full px-4 py-3 rounded-lg bg-zinc-800 border transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.name
                ? 'border-red-500 focus:border-red-500'
                : 'border-zinc-700 focus:border-blue-500'
            }`}
            placeholder={t('contact_form_name_placeholder')}
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-400">{errors.name}</p>
          )}
        </div>

        <div>
          <label htmlFor="discord" className="block text-sm font-medium text-zinc-300 mb-2">
            {t('contact_form_discord_label')} *
          </label>
          <input
            type="text"
            id="discord"
            name="discord"
            value={formData.discord}
            onChange={handleChange}
            className={`w-full px-4 py-3 rounded-lg bg-zinc-800 border transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.discord
                ? 'border-red-500 focus:border-red-500'
                : 'border-zinc-700 focus:border-blue-500'
            }`}
            placeholder={t('contact_form_discord_placeholder')}
          />
          {errors.discord && (
            <p className="mt-1 text-sm text-red-400">{errors.discord}</p>
          )}
        </div>
      </div>

      <div>
        <label htmlFor="subject" className="block text-sm font-medium text-zinc-300 mb-2">
          {t('contact_form_subject_label')} *
        </label>
        <input
          type="text"
          id="subject"
          name="subject"
          value={formData.subject}
          onChange={handleChange}
          className={`w-full px-4 py-3 rounded-lg bg-zinc-800 border transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            errors.subject
              ? 'border-red-500 focus:border-red-500'
              : 'border-zinc-700 focus:border-blue-500'
          }`}
          placeholder={t('contact_form_subject_placeholder')}
        />
        {errors.subject && (
          <p className="mt-1 text-sm text-red-400">{errors.subject}</p>
        )}
      </div>

      <div>
        <label htmlFor="message" className="block text-sm font-medium text-zinc-300 mb-2">
          {t('contact_form_message_label')} *
        </label>
        <textarea
          id="message"
          name="message"
          rows={6}
          value={formData.message}
          onChange={handleChange}
          className={`w-full px-4 py-3 rounded-lg bg-zinc-800 border transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 resize-vertical ${
            errors.message
              ? 'border-red-500 focus:border-red-500'
              : 'border-zinc-700 focus:border-blue-500'
          }`}
          placeholder={t('contact_form_message_placeholder')}
        />
        {errors.message && (
          <p className="mt-1 text-sm text-red-400">{errors.message}</p>
        )}
      </div>

      <button
        type="submit"
        disabled={isSubmitting}
        className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white font-medium py-3 px-6 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-zinc-900"
      >
        {isSubmitting ? (
          <span className="flex items-center justify-center">
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {t('contact_form_submitting')}
          </span>
        ) : (
          t('contact_form_submit_button')
        )}
      </button>
    </form>
  )
}
