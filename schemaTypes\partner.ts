import {defineField, defineType} from 'sanity'

export default defineType({
  name: 'partner',
  title: 'Partner',
  type: 'document',
  fields: [
    defineField({
      name: 'order',
      title: 'Sort Order',
      type: 'number',
      validation: (Rule) => Rule.required(),
      initialValue: 100,
    }),
    defineField({
      name: 'name',
      title: 'Partner Name (Russian)',
      type: 'string',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'en_name',
      title: 'Partner Name (English)',
      type: 'string',
    }),
    defineField({
      name: 'logo',
      title: 'Partner Logo',
      type: 'image',
      options: {
        hotspot: true,
      },
    }),
    defineField({
      name: 'description',
      title: 'Description (Russian)',
      type: 'array',
      of: [{ type: 'block' }],
    }),
    defineField({
      name: 'en_description',
      title: 'Description (English)',
      type: 'array',
      of: [{ type: 'block' }],
    }),
    defineField({
      name: 'website',
      title: 'Website URL',
      type: 'url',
    }),
  ],
  preview: {
    select: {
      title: 'name',
      media: 'logo',
    },
  },
}) 