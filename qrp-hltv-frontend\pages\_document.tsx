import { Html, Head, Main, NextScript } from 'next/document'

export default function Document() {
  return (
    <Html lang="en">
      <Head>
        {/* Favicon and App Icons */}
        <link rel="icon" type="image/x-icon" href="/favicon.ico" />
        <link rel="icon" type="image/svg+xml" href="/favicons/favicon.svg" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicons/favicon-16x16.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicons/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="96x96" href="/favicons/favicon-96x96.png" />

        {/* Apple Touch Icons */}
        <link rel="apple-touch-icon" sizes="180x180" href="/favicons/apple-touch-icon-180x180.png" />
        
        {/* Android Chrome Icons */}
        <link rel="icon" type="image/png" sizes="96x96" href="/favicons/android-chrome-96x96.png" />
        <link rel="icon" type="image/png" sizes="192x192" href="/favicons/android-chrome-192x192.png" />
        <link rel="icon" type="image/png" sizes="512x512" href="/favicons/android-chrome-512x512.png" />

        {/* Microsoft Tiles */}
        <meta name="msapplication-TileColor" content="#18181b" />
        <meta name="msapplication-TileImage" content="/favicons/android-chrome-192x192.png" />
        
        {/* Web App Manifest */}
        <link rel="manifest" href="/manifest.json" />
        
        {/* Theme Colors */}
        <meta name="theme-color" content="#18181b" />
        <meta name="msapplication-navbutton-color" content="#18181b" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        
        {/* App Meta */}
        <meta name="application-name" content="QRP HLTV" />
        <meta name="apple-mobile-web-app-title" content="QRP HLTV" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        
        {/* SEO Meta Tags */}
        <meta name="description" content="QRP HLTV - CarX Drift Racing tournaments, news, and statistics platform" />
        <meta name="keywords" content="CarX, Drift Racing, QRP, HLTV, tournaments, esports, racing" />
        <meta name="author" content="QRP" />
        
        {/* Open Graph Meta Tags */}
        <meta property="og:type" content="website" />
        <meta property="og:site_name" content="QRP HLTV" />
        <meta property="og:image" content="/favicons/android-chrome-512x512.png" />
        <meta property="og:image:width" content="512" />
        <meta property="og:image:height" content="512" />

        {/* Twitter Card Meta Tags */}
        <meta name="twitter:card" content="summary" />
        <meta name="twitter:image" content="/favicons/android-chrome-512x512.png" />
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  )
}
